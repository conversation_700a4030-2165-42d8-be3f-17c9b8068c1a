const { babelOptimizerPlugin } = require("@graphql-codegen/client-preset");

module.exports = (api) => {
  const babelEnv = api.env();
  api.cache.never();

  const isDev = process.env.BABEL_ENV === "development";

  const plugins = [
    [
      "@babel/plugin-transform-react-jsx",
      {
        runtime: "automatic",
      },
    ],
    ["@babel/plugin-proposal-decorators", { legacy: true }],
    "@vjpr/babel-plugin-parameter-decorator",
    [
      "module-resolver",
      {
        extensions: [".ios.js", ".android.js", ".js", ".ts", ".tsx", ".json"],
        alias: {
          // react-native-quick-crypto package aliases
          crypto: "react-native-quick-crypto",
          stream: "stream-browserify",
          buffer: "@craftzdog/react-native-buffer",

          Assets: "./src/Assets",
          Components: "./src/Components",
          Repo: "./src/Repo",
          Services: "./src/Services",
          Features: "./src/Features",
          Screens: "./src/Screens",
          Constants: "./src/Constants",
          Hooks: "./src/Hooks",
          Utils: "./src/Utils",
          Translation: "./src/Translation",
          Trackings: "./src/Trackings",
          Theme: "./src/Theme",
          Player: "./src/Player",
          Video: "./src/Video",
          Libs: "./src/Libs",
          ScreenObjects: "./e2e/tests/screenObjects",
          Composites: "./src/Composites",
          Migrations: "./src/Migrations",
        },
      },
    ],
    "react-native-reanimated/plugin",
  ];
  //change to 'production' to check if this is working in 'development' mode
  if (!isDev) {
    plugins.push(["transform-remove-console", { exclude: ["error"] }]);
  }

  return {
    presets: [
       [
        'babel-preset-expo',
        {
          unstable_transformImportMeta: true,
        },
      ],
    ],
    plugins,
  };
};
