/**
 * @format
 */

import 'reflect-metadata'
import 'react-native-url-polyfill/auto'
import 'react-native-gesture-handler'
import 'react-native-reanimated' // Follow this https://github.com/software-mansion/react-native-reanimated/issues/4836
import './src/Translation/setup'

import { Alert, AppRegistry, Platform } from 'react-native'

// Fix expo image warning
process.env.EXPO_OS = Platform.OS

import crashlytics from '@react-native-firebase/crashlytics'
import {
  setJSExceptionHandler,
  setNativeExceptionHandler,
} from 'react-native-exception-handler'

import { restartApp } from 'Utils/device'
import Sentry from 'Trackings/Sentry'
import { TrackPlayer } from 'Player/Services/TrackPlayerService'

import { name as appName } from './app.json'
import App from './src/App'
import patch from './src/Utils/patch'
import { PlaybackService } from 'Player'

// TODO move this to AppService later
import './src/AppService'
import './src/CarPlay'
import './src/AppShortcut'

patch()

const errorHandler = (e, isFatal) => {
  if (isFatal) {
    crashlytics().recordError(e)
    Sentry.captureException({
      message: 'errorHandler',
      data: {
        error: e,
      },
    })
    Alert.alert(
      'Unexpected error occurred',
      `
        Error: ${isFatal ? 'Fatal:' : ''} ${e?.name} ${e?.message}
        We have reported this to our team ! Please close the app and start again!
        `,
      [
        {
          text: 'Restart',
          onPress: () => restartApp(),
        },
      ],
    )
  } else {
    crashlytics().recordError(e)
    Sentry.captureException({
      message: 'errorHandler',
      data: {
        error: e,
      },
    })
  }
}

setJSExceptionHandler(errorHandler, false)
setNativeExceptionHandler(errorHandler, false)

const WrappedApp = Sentry.wrap(App)

AppRegistry.registerComponent(appName, () => WrappedApp)
TrackPlayer.registerPlaybackService(() => PlaybackService)
