import AppsFlyerLib
import CarPlay
import CleverTapReact
import CleverTapSDK
import Expo
import ExpoModulesCore
import FBSDKCoreKit
import GoogleSignIn
import RNFBFirestore
import React
import ReactAppDependencyProvider
import React_RCTAppDelegate
import SDWebImageWebPCoder
import UIKit
import appcenter_core

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
  var window: UIWindow?
  // React Native infrastructure - shared across scenes
  var reactNativeDelegate: ReactNativeDelegate?
  var reactNativeFactory: RCTReactNativeFactory?

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
    // Initialize React Native delegate properly
    let delegate = ReactNativeDelegate()
    delegate.dependencyProvider = RCTAppDependencyProvider()

    // Create factory with proper delegate
    let factory = RCTReactNativeFactory(delegate: delegate)

    // Store references
    reactNativeDelegate = delegate
    reactNativeFactory = factory

    // Prevent automatic window creation for CarPlay scene support
    // In scene-based architecture, AppDelegate only sets up infrastructure
    // Scene delegates handle view/window creation

    // --- Custom initialization code migrated from Obj-C ---

    // AppCenter registration
    AppCenterReactNative.register()

    // Facebook SDK initialization
    ApplicationDelegate.shared.application(
      application, didFinishLaunchingWithOptions: launchOptions)

    // CleverTap integration
    CleverTap.autoIntegrate()
    CleverTapReactManager.sharedInstance().applicationDidLaunch(options: launchOptions)

    // Register SDWebImage WebP coder
    let webPCoder = SDImageWebPCoder.shared
    SDImageCodersManager.shared.addCoder(webPCoder)

    // Firebase & Firestore configuration
    FirebaseApp.configure()
    //    configureFirestoreSettings()

    return true
  }

  //  override func sourceURL(for bridge: RCTBridge) -> URL? {
  //    self.bundleURL()
  //  }
  //
  //  override func bundleURL() -> URL? {
  //    #if DEBUG
  //      RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
  //    #else
  //      Bundle.main.url(forResource: "main", withExtension: "jsbundle")
  //    #endif
  //  }

  //  func configureFirestoreSettings() {
  //    guard let app = FirebaseApp.app() else { return }
  //    // Assuming RNFBFirestoreCommon is accessible in Swift:
  //    let firestore = RNFBFirestoreCommon.getFirestoreFor(app, databaseId: "asia-southeast-1")
  //    let settings = FirestoreSettings()
  //    settings.dispatchQueue = RNFBFirestoreCommon.getFirestoreQueue()
  //
  //    // Configure persistent cache settings (adjust if the API differs)
  //    let cacheSettings = PersistentCacheSettings(
  //      sizeBytes: NSNumber(value: FirestoreCacheSizeUnlimited))
  //    settings.cacheSettings = cacheSettings
  //    firestore!.settings = settings
  //
  //    if let indexManager = firestore?.persistentCacheIndexManager {
  //      indexManager.enableIndexAutoCreation()
  //    }
  //  }

  // MARK: - URL Handling

  func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bool {
    if ApplicationDelegate.shared.application(app, open: url, options: options) {
      return true
    }
    if Auth.auth().canHandle(url) {
      return true
    }
    if RCTLinkingManager.application(app, open: url, options: options) {
      return true
    }
    if GIDSignIn.sharedInstance.handle(url) {
      return true
    }

    // Let AppsFlyer handle the URL

    AppsFlyerLib.shared().handleOpen(url, options: options)

    // Dummy loop from Obj-C (if needed for side effects)
    for _ in UIFont.familyNames {}

    return false
  }

  func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {
    AppsFlyerLib.shared().continue(
      userActivity,
      restorationHandler: { (objects: [Any]?) in
        restorationHandler(objects as? [UIUserActivityRestoring])
      })
    return true
  }

  // MARK: - Remote Notifications

  func application(
    _ application: UIApplication,
    didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
  ) {

    //    AppsFlyerLib.shared().registerUninstall(deviceToken)
    Auth.auth().setAPNSToken(deviceToken, type: .prod)
    Messaging.messaging().setAPNSToken(deviceToken, type: .prod)
  }

  func application(
    _ application: UIApplication,
    didReceiveRemoteNotification userInfo: [AnyHashable: Any],
    fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
  ) {
    if Auth.auth().canHandleNotification(userInfo) {
      completionHandler(.noData)
      return
    }
    if let cleverTapInstance = CleverTap.sharedInstance(),
      cleverTapInstance.isCleverTapNotification(userInfo)
    {
      completionHandler(.noData)
      return
    }

    if let aps = userInfo["aps"] as? [String: Any],
      let contentAvailable = aps["content-available"] as? Int, contentAvailable == 1
    {
      let currentBadgeCount = UserDefaults.standard.integer(forKey: "BadgeNumber")
      if let badgeSpecific = userInfo["badge"] as? NSNumber {
        UIApplication.shared.applicationIconBadgeNumber = badgeSpecific.intValue
      } else {
        var badgeIncrement: NSNumber?
        if let badgeIncData = (userInfo as NSDictionary).value(forKeyPath: "custom.a.badge_inc")
          as? NSNumber
        {
          badgeIncrement = badgeIncData
        } else if let badgeIncOS = (userInfo as NSDictionary).value(forKeyPath: "custom.badge_inc")
          as? NSNumber
        {
          badgeIncrement = badgeIncOS
        }
        if let badgeInc = badgeIncrement {
          let newBadgeCount = currentBadgeCount + badgeInc.intValue
          UIApplication.shared.applicationIconBadgeNumber = newBadgeCount
        }
      }
      completionHandler(.newData)
    } else {
      completionHandler(.noData)
    }
    UserDefaults.standard.set(
      UIApplication.shared.applicationIconBadgeNumber, forKey: "BadgeNumber")
  }

  // MARK: - Location Updates

  // If using CLLocationManager, ensure this delegate method is wired up.
  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    if let location = locations.last {
      CleverTap.setLocation(location.coordinate)
    }
  }

  // MARK: - UISceneSession Lifecycle (CarPlay and Phone Scenes)

  func application(
    _ application: UIApplication,
    configurationForConnecting connectingSceneSession: UISceneSession,
    options: UIScene.ConnectionOptions
  ) -> UISceneConfiguration {
    print("configurationForConnectingSceneSession: \(connectingSceneSession.role)")
    if #available(iOS 14.0, *),
      connectingSceneSession.role == CarPlay.UISceneSession.Role.carTemplateApplication
    {
      let scene = UISceneConfiguration(
        name: "CarPlaySceneConfiguration", sessionRole: connectingSceneSession.role)
      scene.delegateClass = CarPlaySceneDelegate.self
      return scene
    }
    let scene = UISceneConfiguration(
      name: "PhoneSceneConfiguration", sessionRole: connectingSceneSession.role)
    scene.delegateClass = PhoneSceneDelegate.self
    return scene
  }

  // MARK: - Helper Methods for Scenes

  /// Creates a React Native view for use in scenes
  /// - Parameters:
  ///   - moduleName: The React Native module name (default: "FonosMobile")
  ///   - initialProperties: Initial properties for the view
  ///   - launchOptions: Launch options from app startup
  /// - Returns: A configured React Native view
  func createReactNativeView(
    moduleName: String = "FonosMobile",
    initialProperties: [String: Any] = [:],
    launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> UIView? {
    guard let factory = reactNativeFactory else { return nil }
    return factory.rootViewFactory.view(
      withModuleName: moduleName,
      initialProperties: initialProperties,
      launchOptions: launchOptions
    )
  }
}

class ReactNativeDelegate: RCTDefaultReactNativeFactoryDelegate {
  override func sourceURL(for bridge: RCTBridge) -> URL? {
    self.bundleURL()
  }

  override func bundleURL() -> URL? {
    #if DEBUG
      RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #else
      Bundle.main.url(forResource: "main", withExtension: "jsbundle")
    #endif
  }
}