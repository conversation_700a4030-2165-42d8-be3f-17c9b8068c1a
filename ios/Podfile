ENV['USE_FRAMEWORKS'] = 'static'
ENV['RCT_NEW_ARCH_ENABLED'] = '1'

require File.join(File.dirname(`node --print "require.resolve('expo/package.json')"`), "scripts/autolinking")

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

# Use it to require both react-native's and this package's scripts:
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

project 'FonosMobile', {
  'Debug' => :debug,
  'Release' => :release,

  'DebugStaging' => :debug,
  'ReleaseStaging' => :release,

  'DebugSandbox' => :debug,
  'ReleaseSandbox' => :release,
  
  'DebugProduction' => :debug,
  'ReleaseProduction' => :release,
}

setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  # 'LocationAlways',
  # 'LocationWhenInUse',
  # 'MediaLibrary',
  # 'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary',
  'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])

target 'FonosMobile' do
  # Shared Element transition does not work after adding @react-native-firebase 
  # https://github.com/software-mansion/react-native-reanimated/issues/4966
  pre_install do |installer|
    installer.pod_targets.each do |pod|
      if pod.name.eql?('RNScreens')
        def pod.build_type
          Pod::BuildType.static_library
        end
      end
    end
  end
  use_expo_modules!

  if ENV['EXPO_USE_COMMUNITY_AUTOLINKING'] == '1'
    config_command = ['node', '-e', "process.argv=['', '', 'config'];require('@react-native-community/cli').run()"];
  else
    config_command = [
      'npx',
      'expo-modules-autolinking',
      'react-native-config',
      '--json',
      '--platform',
      'ios'
    ]
  end

  config = use_native_modules!(config_command)

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  pod 'ZendeskAnswerBotSDK'
  pod 'ZendeskSupportSDK'
  pod 'ZendeskChatSDK'
  pod 'Kingfisher', '~> 7.9.1'
  pod 'GzipSwift'
  pod 'AppsFlyerFramework'

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => true
    )

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings["ONLY_ACTIVE_ARCH"] = "NO"
      end
    end
  end
end

target 'NotificationService' do
  pod 'CTNotificationService'
  pod 'CleverTap-iOS-SDK'
  pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
end
