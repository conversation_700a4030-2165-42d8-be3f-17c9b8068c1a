const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config')
const { getSentryExpoConfig } = require('@sentry/react-native/metro')

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */

const expoConfig = getSentryExpoConfig(__dirname, {
  annotateReactComponents: true,
})
const defaultConfig = getDefaultConfig(__dirname)

const config = {
  transformer: {
    ...expoConfig.transformer,
    ...defaultConfig.transformer,
    assetPlugins: ['expo-asset/tools/hashAssetFiles'],
    unstable_allowRequireContext: true, // Support require.context
  },
}

const mergedConfig = mergeConfig(defaultConfig, expoConfig, config)

mergedConfig.resolver.unstable_enablePackageExports = false;

module.exports = mergedConfig
