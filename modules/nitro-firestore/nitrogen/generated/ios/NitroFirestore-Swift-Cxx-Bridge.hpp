///
/// NitroFirestore-Swift-Cxx-Bridge.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON>av<PERSON> @ Margelo
///

#pragma once

// Forward declarations of C++ defined types
// Forward declaration of `DocRef` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct DocRef; }
// Forward declaration of `FilterGroup` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct FilterGroup; }
// Forward declaration of `HybridFirestoreSubscriptionSpec` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { class HybridFirestoreSubscriptionSpec; }
// Forward declaration of `HybridNitroFirestoreSpec` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { class HybridNitroFirestoreSpec; }
// Forward declaration of `ObserverOptions` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct ObserverOptions; }
// Forward declaration of `PullOptions` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct PullOptions; }
// Forward declaration of `QueryParams` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct QueryParams; }
// Forward declaration of `Sort` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct Sort; }
// Forward declaration of `StringHolder` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct StringHolder; }
// Forward declaration of `ValueType` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct ValueType; }
// Forward declaration of `Where` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct Where; }

// Forward declarations of Swift defined types
// Forward declaration of `HybridFirestoreSubscriptionSpec_cxx` to properly resolve imports.
namespace NitroFirestore { class HybridFirestoreSubscriptionSpec_cxx; }
// Forward declaration of `HybridNitroFirestoreSpec_cxx` to properly resolve imports.
namespace NitroFirestore { class HybridNitroFirestoreSpec_cxx; }

// Include C++ defined types
#include "DocRef.hpp"
#include "FilterGroup.hpp"
#include "HybridFirestoreSubscriptionSpec.hpp"
#include "HybridNitroFirestoreSpec.hpp"
#include "ObserverOptions.hpp"
#include "PullOptions.hpp"
#include "QueryParams.hpp"
#include "Sort.hpp"
#include "StringHolder.hpp"
#include "ValueType.hpp"
#include "Where.hpp"
#include <NitroModules/Promise.hpp>
#include <NitroModules/PromiseHolder.hpp>
#include <NitroModules/Result.hpp>
#include <exception>
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <vector>

/**
 * Contains specialized versions of C++ templated types so they can be accessed from Swift,
 * as well as helper functions to interact with those C++ types from Swift.
 */
namespace margelo::nitro::nitrofirestore::bridge::swift {

  // pragma MARK: std::shared_ptr<Promise<void>>
  /**
   * Specialized version of `std::shared_ptr<Promise<void>>`.
   */
  using std__shared_ptr_Promise_void__ = std::shared_ptr<Promise<void>>;
  inline std::shared_ptr<Promise<void>> create_std__shared_ptr_Promise_void__() {
    return Promise<void>::create();
  }
  inline PromiseHolder<void> wrap_std__shared_ptr_Promise_void__(std::shared_ptr<Promise<void>> promise) {
    return PromiseHolder<void>(std::move(promise));
  }
  
  // pragma MARK: std::function<void()>
  /**
   * Specialized version of `std::function<void()>`.
   */
  using Func_void = std::function<void()>;
  /**
   * Wrapper class for a `std::function<void()>`, this can be used from Swift.
   */
  class Func_void_Wrapper final {
  public:
    explicit Func_void_Wrapper(std::function<void()>&& func): _function(std::make_shared<std::function<void()>>(std::move(func))) {}
    inline void call() const {
      _function->operator()();
    }
  private:
    std::shared_ptr<std::function<void()>> _function;
  };
  Func_void create_Func_void(void* _Nonnull swiftClosureWrapper);
  inline Func_void_Wrapper wrap_Func_void(Func_void value) {
    return Func_void_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::function<void(const std::exception_ptr& /* error */)>
  /**
   * Specialized version of `std::function<void(const std::exception_ptr&)>`.
   */
  using Func_void_std__exception_ptr = std::function<void(const std::exception_ptr& /* error */)>;
  /**
   * Wrapper class for a `std::function<void(const std::exception_ptr& / * error * /)>`, this can be used from Swift.
   */
  class Func_void_std__exception_ptr_Wrapper final {
  public:
    explicit Func_void_std__exception_ptr_Wrapper(std::function<void(const std::exception_ptr& /* error */)>&& func): _function(std::make_shared<std::function<void(const std::exception_ptr& /* error */)>>(std::move(func))) {}
    inline void call(std::exception_ptr error) const {
      _function->operator()(error);
    }
  private:
    std::shared_ptr<std::function<void(const std::exception_ptr& /* error */)>> _function;
  };
  Func_void_std__exception_ptr create_Func_void_std__exception_ptr(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__exception_ptr_Wrapper wrap_Func_void_std__exception_ptr(Func_void_std__exception_ptr value) {
    return Func_void_std__exception_ptr_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>
  /**
   * Specialized version of `std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>`.
   */
  using std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ = std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>;
  std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec> create_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_(void* _Nonnull swiftUnsafePointer);
  void* _Nonnull get_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_(std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ cppType);
  
  // pragma MARK: std::weak_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>
  using std__weak_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ = std::weak_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>;
  inline std__weak_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ weakify_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& strong) { return strong; }
  
  // pragma MARK: Result<std::shared_ptr<Promise<void>>>
  using Result_std__shared_ptr_Promise_void___ = Result<std::shared_ptr<Promise<void>>>;
  inline Result_std__shared_ptr_Promise_void___ create_Result_std__shared_ptr_Promise_void___(const std::shared_ptr<Promise<void>>& value) {
    return Result<std::shared_ptr<Promise<void>>>::withValue(value);
  }
  inline Result_std__shared_ptr_Promise_void___ create_Result_std__shared_ptr_Promise_void___(const std::exception_ptr& error) {
    return Result<std::shared_ptr<Promise<void>>>::withError(error);
  }
  
  // pragma MARK: std::shared_ptr<Promise<std::string>>
  /**
   * Specialized version of `std::shared_ptr<Promise<std::string>>`.
   */
  using std__shared_ptr_Promise_std__string__ = std::shared_ptr<Promise<std::string>>;
  inline std::shared_ptr<Promise<std::string>> create_std__shared_ptr_Promise_std__string__() {
    return Promise<std::string>::create();
  }
  inline PromiseHolder<std::string> wrap_std__shared_ptr_Promise_std__string__(std::shared_ptr<Promise<std::string>> promise) {
    return PromiseHolder<std::string>(std::move(promise));
  }
  
  // pragma MARK: std::function<void(const std::string& /* result */)>
  /**
   * Specialized version of `std::function<void(const std::string&)>`.
   */
  using Func_void_std__string = std::function<void(const std::string& /* result */)>;
  /**
   * Wrapper class for a `std::function<void(const std::string& / * result * /)>`, this can be used from Swift.
   */
  class Func_void_std__string_Wrapper final {
  public:
    explicit Func_void_std__string_Wrapper(std::function<void(const std::string& /* result */)>&& func): _function(std::make_shared<std::function<void(const std::string& /* result */)>>(std::move(func))) {}
    inline void call(std::string result) const {
      _function->operator()(result);
    }
  private:
    std::shared_ptr<std::function<void(const std::string& /* result */)>> _function;
  };
  Func_void_std__string create_Func_void_std__string(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__string_Wrapper wrap_Func_void_std__string(Func_void_std__string value) {
    return Func_void_std__string_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::optional<std::string>
  /**
   * Specialized version of `std::optional<std::string>`.
   */
  using std__optional_std__string_ = std::optional<std::string>;
  inline std::optional<std::string> create_std__optional_std__string_(const std::string& value) {
    return std::optional<std::string>(value);
  }
  
  // pragma MARK: std::vector<Where>
  /**
   * Specialized version of `std::vector<Where>`.
   */
  using std__vector_Where_ = std::vector<Where>;
  inline std::vector<Where> create_std__vector_Where_(size_t size) {
    std::vector<Where> vector;
    vector.reserve(size);
    return vector;
  }
  
  // pragma MARK: std::optional<std::vector<Where>>
  /**
   * Specialized version of `std::optional<std::vector<Where>>`.
   */
  using std__optional_std__vector_Where__ = std::optional<std::vector<Where>>;
  inline std::optional<std::vector<Where>> create_std__optional_std__vector_Where__(const std::vector<Where>& value) {
    return std::optional<std::vector<Where>>(value);
  }
  
  // pragma MARK: std::vector<FilterGroup>
  /**
   * Specialized version of `std::vector<FilterGroup>`.
   */
  using std__vector_FilterGroup_ = std::vector<FilterGroup>;
  inline std::vector<FilterGroup> create_std__vector_FilterGroup_(size_t size) {
    std::vector<FilterGroup> vector;
    vector.reserve(size);
    return vector;
  }
  
  // pragma MARK: std::optional<std::vector<FilterGroup>>
  /**
   * Specialized version of `std::optional<std::vector<FilterGroup>>`.
   */
  using std__optional_std__vector_FilterGroup__ = std::optional<std::vector<FilterGroup>>;
  inline std::optional<std::vector<FilterGroup>> create_std__optional_std__vector_FilterGroup__(const std::vector<FilterGroup>& value) {
    return std::optional<std::vector<FilterGroup>>(value);
  }
  
  // pragma MARK: std::vector<Sort>
  /**
   * Specialized version of `std::vector<Sort>`.
   */
  using std__vector_Sort_ = std::vector<Sort>;
  inline std::vector<Sort> create_std__vector_Sort_(size_t size) {
    std::vector<Sort> vector;
    vector.reserve(size);
    return vector;
  }
  
  // pragma MARK: std::optional<std::vector<Sort>>
  /**
   * Specialized version of `std::optional<std::vector<Sort>>`.
   */
  using std__optional_std__vector_Sort__ = std::optional<std::vector<Sort>>;
  inline std::optional<std::vector<Sort>> create_std__optional_std__vector_Sort__(const std::vector<Sort>& value) {
    return std::optional<std::vector<Sort>>(value);
  }
  
  // pragma MARK: std::optional<double>
  /**
   * Specialized version of `std::optional<double>`.
   */
  using std__optional_double_ = std::optional<double>;
  inline std::optional<double> create_std__optional_double_(const double& value) {
    return std::optional<double>(value);
  }
  
  // pragma MARK: std::vector<StringHolder>
  /**
   * Specialized version of `std::vector<StringHolder>`.
   */
  using std__vector_StringHolder_ = std::vector<StringHolder>;
  inline std::vector<StringHolder> create_std__vector_StringHolder_(size_t size) {
    std::vector<StringHolder> vector;
    vector.reserve(size);
    return vector;
  }
  
  // pragma MARK: std::optional<std::vector<StringHolder>>
  /**
   * Specialized version of `std::optional<std::vector<StringHolder>>`.
   */
  using std__optional_std__vector_StringHolder__ = std::optional<std::vector<StringHolder>>;
  inline std::optional<std::vector<StringHolder>> create_std__optional_std__vector_StringHolder__(const std::vector<StringHolder>& value) {
    return std::optional<std::vector<StringHolder>>(value);
  }
  
  // pragma MARK: std::optional<QueryParams>
  /**
   * Specialized version of `std::optional<QueryParams>`.
   */
  using std__optional_QueryParams_ = std::optional<QueryParams>;
  inline std::optional<QueryParams> create_std__optional_QueryParams_(const QueryParams& value) {
    return std::optional<QueryParams>(value);
  }
  
  // pragma MARK: std::optional<bool>
  /**
   * Specialized version of `std::optional<bool>`.
   */
  using std__optional_bool_ = std::optional<bool>;
  inline std::optional<bool> create_std__optional_bool_(const bool& value) {
    return std::optional<bool>(value);
  }
  
  // pragma MARK: std::optional<ObserverOptions>
  /**
   * Specialized version of `std::optional<ObserverOptions>`.
   */
  using std__optional_ObserverOptions_ = std::optional<ObserverOptions>;
  inline std::optional<ObserverOptions> create_std__optional_ObserverOptions_(const ObserverOptions& value) {
    return std::optional<ObserverOptions>(value);
  }
  
  // pragma MARK: std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>
  /**
   * Specialized version of `std::function<void(const std::string&, const std::string&, const std::optional<std::string>&)>`.
   */
  using Func_void_std__string_std__string_std__optional_std__string_ = std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>;
  /**
   * Wrapper class for a `std::function<void(const std::string& / * data * /, const std::string& / * collectionKey * /, const std::optional<std::string>& / * docPath * /)>`, this can be used from Swift.
   */
  class Func_void_std__string_std__string_std__optional_std__string__Wrapper final {
  public:
    explicit Func_void_std__string_std__string_std__optional_std__string__Wrapper(std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>&& func): _function(std::make_shared<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>>(std::move(func))) {}
    inline void call(std::string data, std::string collectionKey, std::optional<std::string> docPath) const {
      _function->operator()(data, collectionKey, docPath);
    }
  private:
    std::shared_ptr<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>> _function;
  };
  Func_void_std__string_std__string_std__optional_std__string_ create_Func_void_std__string_std__string_std__optional_std__string_(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__string_std__string_std__optional_std__string__Wrapper wrap_Func_void_std__string_std__string_std__optional_std__string_(Func_void_std__string_std__string_std__optional_std__string_ value) {
    return Func_void_std__string_std__string_std__optional_std__string__Wrapper(std::move(value));
  }
  
  // pragma MARK: std::optional<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>>
  /**
   * Specialized version of `std::optional<std::function<void(const std::string& / * data * /, const std::string& / * collectionKey * /, const std::optional<std::string>& / * docPath * /)>>`.
   */
  using std__optional_std__function_void_const_std__string_____data_____const_std__string_____collectionKey_____const_std__optional_std__string______docPath______ = std::optional<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>>;
  inline std::optional<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>> create_std__optional_std__function_void_const_std__string_____data_____const_std__string_____collectionKey_____const_std__optional_std__string______docPath______(const std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>& value) {
    return std::optional<std::function<void(const std::string& /* data */, const std::string& /* collectionKey */, const std::optional<std::string>& /* docPath */)>>(value);
  }
  
  // pragma MARK: std::shared_ptr<Promise<bool>>
  /**
   * Specialized version of `std::shared_ptr<Promise<bool>>`.
   */
  using std__shared_ptr_Promise_bool__ = std::shared_ptr<Promise<bool>>;
  inline std::shared_ptr<Promise<bool>> create_std__shared_ptr_Promise_bool__() {
    return Promise<bool>::create();
  }
  inline PromiseHolder<bool> wrap_std__shared_ptr_Promise_bool__(std::shared_ptr<Promise<bool>> promise) {
    return PromiseHolder<bool>(std::move(promise));
  }
  
  // pragma MARK: std::function<void(bool /* result */)>
  /**
   * Specialized version of `std::function<void(bool)>`.
   */
  using Func_void_bool = std::function<void(bool /* result */)>;
  /**
   * Wrapper class for a `std::function<void(bool / * result * /)>`, this can be used from Swift.
   */
  class Func_void_bool_Wrapper final {
  public:
    explicit Func_void_bool_Wrapper(std::function<void(bool /* result */)>&& func): _function(std::make_shared<std::function<void(bool /* result */)>>(std::move(func))) {}
    inline void call(bool result) const {
      _function->operator()(result);
    }
  private:
    std::shared_ptr<std::function<void(bool /* result */)>> _function;
  };
  Func_void_bool create_Func_void_bool(void* _Nonnull swiftClosureWrapper);
  inline Func_void_bool_Wrapper wrap_Func_void_bool(Func_void_bool value) {
    return Func_void_bool_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::optional<PullOptions>
  /**
   * Specialized version of `std::optional<PullOptions>`.
   */
  using std__optional_PullOptions_ = std::optional<PullOptions>;
  inline std::optional<PullOptions> create_std__optional_PullOptions_(const PullOptions& value) {
    return std::optional<PullOptions>(value);
  }
  
  // pragma MARK: std::vector<DocRef>
  /**
   * Specialized version of `std::vector<DocRef>`.
   */
  using std__vector_DocRef_ = std::vector<DocRef>;
  inline std::vector<DocRef> create_std__vector_DocRef_(size_t size) {
    std::vector<DocRef> vector;
    vector.reserve(size);
    return vector;
  }
  
  // pragma MARK: std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>
  /**
   * Specialized version of `std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>`.
   */
  using std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec___ = std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>;
  inline std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>> create_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec___() {
    return Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>::create();
  }
  inline PromiseHolder<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>> wrap_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec___(std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>> promise) {
    return PromiseHolder<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>(std::move(promise));
  }
  
  // pragma MARK: std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& /* result */)>
  /**
   * Specialized version of `std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>&)>`.
   */
  using Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ = std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& /* result */)>;
  /**
   * Wrapper class for a `std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& / * result * /)>`, this can be used from Swift.
   */
  class Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec__Wrapper final {
  public:
    explicit Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec__Wrapper(std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& /* result */)>&& func): _function(std::make_shared<std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& /* result */)>>(std::move(func))) {}
    inline void call(std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec> result) const {
      _function->operator()(result);
    }
  private:
    std::shared_ptr<std::function<void(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>& /* result */)>> _function;
  };
  Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ create_Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec__Wrapper wrap_Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_(Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec_ value) {
    return Func_void_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec__Wrapper(std::move(value));
  }
  
  // pragma MARK: std::function<void(const std::string& /* docs */, const std::optional<std::string>& /* error */)>
  /**
   * Specialized version of `std::function<void(const std::string&, const std::optional<std::string>&)>`.
   */
  using Func_void_std__string_std__optional_std__string_ = std::function<void(const std::string& /* docs */, const std::optional<std::string>& /* error */)>;
  /**
   * Wrapper class for a `std::function<void(const std::string& / * docs * /, const std::optional<std::string>& / * error * /)>`, this can be used from Swift.
   */
  class Func_void_std__string_std__optional_std__string__Wrapper final {
  public:
    explicit Func_void_std__string_std__optional_std__string__Wrapper(std::function<void(const std::string& /* docs */, const std::optional<std::string>& /* error */)>&& func): _function(std::make_shared<std::function<void(const std::string& /* docs */, const std::optional<std::string>& /* error */)>>(std::move(func))) {}
    inline void call(std::string docs, std::optional<std::string> error) const {
      _function->operator()(docs, error);
    }
  private:
    std::shared_ptr<std::function<void(const std::string& /* docs */, const std::optional<std::string>& /* error */)>> _function;
  };
  Func_void_std__string_std__optional_std__string_ create_Func_void_std__string_std__optional_std__string_(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__string_std__optional_std__string__Wrapper wrap_Func_void_std__string_std__optional_std__string_(Func_void_std__string_std__optional_std__string_ value) {
    return Func_void_std__string_std__optional_std__string__Wrapper(std::move(value));
  }
  
  // pragma MARK: std::shared_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>
  /**
   * Specialized version of `std::shared_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>`.
   */
  using std__shared_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_ = std::shared_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>;
  std::shared_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec> create_std__shared_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_(void* _Nonnull swiftUnsafePointer);
  void* _Nonnull get_std__shared_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_(std__shared_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_ cppType);
  
  // pragma MARK: std::weak_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>
  using std__weak_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_ = std::weak_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>;
  inline std__weak_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_ weakify_std__shared_ptr_margelo__nitro__nitrofirestore__HybridNitroFirestoreSpec_(const std::shared_ptr<margelo::nitro::nitrofirestore::HybridNitroFirestoreSpec>& strong) { return strong; }
  
  // pragma MARK: Result<std::shared_ptr<Promise<std::string>>>
  using Result_std__shared_ptr_Promise_std__string___ = Result<std::shared_ptr<Promise<std::string>>>;
  inline Result_std__shared_ptr_Promise_std__string___ create_Result_std__shared_ptr_Promise_std__string___(const std::shared_ptr<Promise<std::string>>& value) {
    return Result<std::shared_ptr<Promise<std::string>>>::withValue(value);
  }
  inline Result_std__shared_ptr_Promise_std__string___ create_Result_std__shared_ptr_Promise_std__string___(const std::exception_ptr& error) {
    return Result<std::shared_ptr<Promise<std::string>>>::withError(error);
  }
  
  // pragma MARK: Result<std::shared_ptr<Promise<bool>>>
  using Result_std__shared_ptr_Promise_bool___ = Result<std::shared_ptr<Promise<bool>>>;
  inline Result_std__shared_ptr_Promise_bool___ create_Result_std__shared_ptr_Promise_bool___(const std::shared_ptr<Promise<bool>>& value) {
    return Result<std::shared_ptr<Promise<bool>>>::withValue(value);
  }
  inline Result_std__shared_ptr_Promise_bool___ create_Result_std__shared_ptr_Promise_bool___(const std::exception_ptr& error) {
    return Result<std::shared_ptr<Promise<bool>>>::withError(error);
  }
  
  // pragma MARK: Result<std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>>
  using Result_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec____ = Result<std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>>;
  inline Result_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec____ create_Result_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec____(const std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>& value) {
    return Result<std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>>::withValue(value);
  }
  inline Result_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec____ create_Result_std__shared_ptr_Promise_std__shared_ptr_margelo__nitro__nitrofirestore__HybridFirestoreSubscriptionSpec____(const std::exception_ptr& error) {
    return Result<std::shared_ptr<Promise<std::shared_ptr<margelo::nitro::nitrofirestore::HybridFirestoreSubscriptionSpec>>>>::withError(error);
  }

} // namespace margelo::nitro::nitrofirestore::bridge::swift
