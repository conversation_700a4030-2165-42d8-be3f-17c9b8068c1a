///
/// NitroFirestore-Swift-Cxx-Umbrella.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

// Forward declarations of C++ defined types
// Forward declaration of `DocRef` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct DocRef; }
// Forward declaration of `FilterGroup` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct FilterGroup; }
// Forward declaration of `HybridFirestoreSubscriptionSpec` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { class HybridFirestoreSubscriptionSpec; }
// Forward declaration of `HybridNitroFirestoreSpec` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { class HybridNitroFirestoreSpec; }
// Forward declaration of `ListenerQueryParams` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct ListenerQueryParams; }
// Forward declaration of `ObserverOptions` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct ObserverOptions; }
// Forward declaration of `PullOptions` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct PullOptions; }
// Forward declaration of `QueryParams` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct QueryParams; }
// Forward declaration of `Sort` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct Sort; }
// Forward declaration of `StringHolder` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct StringHolder; }
// Forward declaration of `ValueType` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct ValueType; }
// Forward declaration of `Where` to properly resolve imports.
namespace margelo::nitro::nitrofirestore { struct Where; }

// Include C++ defined types
#include "DocRef.hpp"
#include "FilterGroup.hpp"
#include "HybridFirestoreSubscriptionSpec.hpp"
#include "HybridNitroFirestoreSpec.hpp"
#include "ListenerQueryParams.hpp"
#include "ObserverOptions.hpp"
#include "PullOptions.hpp"
#include "QueryParams.hpp"
#include "Sort.hpp"
#include "StringHolder.hpp"
#include "ValueType.hpp"
#include "Where.hpp"
#include <NitroModules/Promise.hpp>
#include <NitroModules/Result.hpp>
#include <exception>
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <vector>

// C++ helpers for Swift
#include "NitroFirestore-Swift-Cxx-Bridge.hpp"

// Common C++ types used in Swift
#include <NitroModules/ArrayBufferHolder.hpp>
#include <NitroModules/AnyMapHolder.hpp>
#include <NitroModules/RuntimeError.hpp>

// Forward declarations of Swift defined types
// Forward declaration of `HybridFirestoreSubscriptionSpec_cxx` to properly resolve imports.
namespace NitroFirestore { class HybridFirestoreSubscriptionSpec_cxx; }
// Forward declaration of `HybridNitroFirestoreSpec_cxx` to properly resolve imports.
namespace NitroFirestore { class HybridNitroFirestoreSpec_cxx; }

// Include Swift defined types
#if __has_include("NitroFirestore-Swift.h")
// This header is generated by Xcode/Swift on every app build.
// If it cannot be found, make sure the Swift module's name (= podspec name) is actually "NitroFirestore".
#include "NitroFirestore-Swift.h"
// Same as above, but used when building with frameworks (`use_frameworks`)
#elif __has_include(<NitroFirestore/NitroFirestore-Swift.h>)
#include <NitroFirestore/NitroFirestore-Swift.h>
#else
#error NitroFirestore's autogenerated Swift header cannot be found! Make sure the Swift module's name (= podspec name) is actually "NitroFirestore", and try building the app first.
#endif
