{"name": "fonosmobile", "version": "1.0.7", "private": true, "scripts": {"start": "npx react-native start", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "biome lint ./src", "format": "biome check --write --unsafe ./src", "check": "biome check ./src", "app": "./scripts/app.sh", "buid-test-app": "./scripts/build-test-app.sh", "run-test-app": "./scripts/run-app-simulator.sh", "run-device-farm": "./scripts/run-device-farm.sh", "zip-test-package": "cd e2e && yarn && yarn pack-zip && cd ../", "pack-secrets": "./scripts/pack-secrets.sh", "unpack-secrets": "./scripts/unpack-secrets.sh", "deploy": "./scripts/deploy.sh", "upload-dsyms": "./scripts/upload_dsyms.sh", "extract:messages": "react-intl-cra 'src/**/messages.{js,jsx,ts,tsx}' -o 'output/messages.json'", "extract:pot": "react-intl-po json2pot 'output/messages.json' -o 'output/messages.pot'", "extract:po": "react-intl-po po2json './locale/*.po' -m './output/messages.json' -o './src/Translation/translation.json'", "compress-images": "./scripts/webp.sh", "all": "watchman watch-del-all && rm -rf package-lock.json && rm -rf node_modules/ && rm -fr $TMPDIR/metro* && yarn && cd ios && rm -rf Pods && rm -rf Podfile.lock && pod install && cd ..", "codegen": "graphql-codegen --config codegen.ts", "pod-install": "cd ios && CIRCLECI=false USE_FRAMEWORKS=static RCT_NEW_ARCH_ENABLED=1 bundle exec pod install --repo-update", "patch-package": "patch-package"}, "dependencies": {"@animatereactnative/marquee": "^0.5.2", "@dr.pogodin/react-native-fs": "2.31.0", "@dr.pogodin/react-native-static-server": "0.19.0", "@feathersjs/authentication-client": "^5.0.29", "@feathersjs/feathers": "^5.0.29", "@feathersjs/rest-client": "^5.0.29", "@formatjs/intl-datetimeformat": "^6.12.5", "@formatjs/intl-getcanonicallocales": "^2.3.0", "@formatjs/intl-locale": "^4.0.0", "@formatjs/intl-numberformat": "^8.10.3", "@formatjs/intl-pluralrules": "^5.2.14", "@formatjs/intl-relativetimeformat": "^11.2.14", "@gorhom/bottom-sheet": "5.0.6", "@invertase/react-native-apple-authentication": "^2.4.0", "@legendapp/list": "^1.1.4", "@meksiabdou/react-native-barcode-mask": "^2.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/analytics": "21.14.0", "@react-native-firebase/app": "21.14.0", "@react-native-firebase/auth": "21.14.0", "@react-native-firebase/crashlytics": "21.14.0", "@react-native-firebase/dynamic-links": "21.14.0", "@react-native-firebase/firestore": "21.14.0", "@react-native-firebase/messaging": "21.14.0", "@react-native-firebase/perf": "21.14.0", "@react-native-firebase/remote-config": "21.14.0", "@react-native-firebase/storage": "21.14.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "v2.0.0-next.4", "@sparkfabrik/react-native-idfa-aaid": "^1.2.0", "@tanstack/query-persist-client-core": "^5.74.4", "@tanstack/react-query": "^5.74.4", "@vmsilva/react-native-track-player": "^4.1.6", "appcenter": "^5.0.1", "axios": "^1.8.1", "bson-objectid": "^2.0.4", "clevertap-react-native": "^3.2.0", "color": "^4.2.3", "deep-diff": "^1.0.2", "deprecated-react-native-prop-types": "^5.0.0", "events": "^3.3.0", "expo": "^53.0.0", "expo-av": "~15.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-media-library": "~17.1.7", "expo-modules-core": "~2.5.0", "expo-screen-capture": "~7.1.5", "feathers-batch": "^1.1.1", "firebase": "^11.6.0", "gql-query-builder": "^3.8.0", "graphql": "^15", "graphql-request": "^4.3.0", "immer": "^10.1.1", "inversify": "6.2.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.5", "lottie-react-native": "^7.2.2", "lunr": "^2.3.9", "lunr-languages": "^1.14.0", "mixpanel-react-native": "^3.0.8", "moment": "^2.30.1", "p-queue": "^8.0.1", "papaparse": "^5.4.1", "query-string": "^9.1.0", "react": "19.0.0", "react-freeze": "^1.0.4", "react-intl": "^6.6.8", "react-intl-formatted-duration": "^4.0.0", "react-markdown": "^8.0.7", "react-native": "0.79.5", "react-native-animateable-text": "0.16.0-beta.0", "react-native-appsflyer": "^6.15.3", "react-native-background-timer": "^2.4.1", "react-native-base64": "^0.2.1", "react-native-blob-util": "^0.19.11", "react-native-bootsplash": "^6.3.7", "react-native-bundle-splitter": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-config": "^1.5.5", "react-native-confirmation-code-field": "^6.7.0", "react-native-country-picker-modal": "^2.0.0", "react-native-device-info": "^14.0.4", "react-native-edge-to-edge": "1.6.0", "react-native-email-link": "^1.16.1", "react-native-exception-handler": "^2.10.10", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "^2.24.0", "react-native-gradient-shimmer": "^2.0.1", "react-native-keyboard-controller": "^1.16.7", "react-native-logs": "^5.3.0", "react-native-mime-types": "^2.5.0", "react-native-mmkv-storage": "^0.11.2", "react-native-music-control": "tuandinh-org/react-native-music-control", "react-native-nitro-firestore": "link:./modules/nitro-firestore", "react-native-nitro-modules": "^0.25.2", "react-native-ntp-client": "^1.0.3", "react-native-onesignal": "^5.2.9", "react-native-pager-view": "6.7.1", "react-native-pdf": "^6.7.7", "react-native-permissions": "^5.2.6", "react-native-platform": "^0.12.2", "react-native-portalize": "^1.0.7", "react-native-purchases": "^8.9.7", "react-native-purchases-ui": "^8.9.7", "react-native-quick-crypto": "^0.7.12", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.0.9", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.2", "react-native-text-ticker": "^1.14.0", "react-native-udp": "^4.1.7", "react-native-url-polyfill": "^2.0.0", "react-native-video": "^6.11.0", "react-native-view-shot": "~4.0.3", "react-native-webp-format": "^1.2.0", "react-native-webview": "13.13.5", "reflect-metadata": "^0.2.2", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "rxjs": "^7.8.1", "validator": "^13.12.0", "zustand": "^4.5.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.18.10", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@biomejs/biome": "2.0.6", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "^4.3.3", "@graphql-codegen/introspection": "4.0.3", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@rnx-kit/align-deps": "^2.5.1", "@types/color": "^3.0.6", "@types/deep-diff": "^1", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.14", "@types/lunr": "^2", "@types/papaparse": "^5.3.14", "@types/prettier": "^2.7.3", "@types/react": "~19.0.10", "@types/react-native-background-timer": "^2.0.2", "@types/react-native-base64": "^0.2.2", "@types/react-native-video": "^5.0.20", "@types/react-test-renderer": "^18.0.0", "@types/validator": "^13.12.0", "@types/webpack-env": "^1.18.5", "@vjpr/babel-plugin-parameter-decorator": "^1.0.15", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-react-native": "^4.0.1", "dotenv": "^16.4.5", "firebase-admin": "^12.3.1", "husky": "^5.2.0", "jest": "^29.6.3", "lint-staged": "^13.3.0", "nitro-codegen": "^0.25.1", "patch-package": "^8.0.0", "react-intl-cra": "^0.3.4", "react-intl-po": "^2.2.2", "react-native-typescript-transformer": "^1.2.13", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "engines": {"node": ">=18", "yarn": ">=3.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-checkout": "echo $HUSKY_GIT_STDIN | lfs-hooks/post-checkout $HUSKY_GIT_PARAMS", "post-commit": "echo $HUSKY_GIT_STDIN | lfs-hooks/post-commit $HUSKY_GIT_PARAMS", "post-merge": "echo $HUSKY_GIT_STDIN | lfs-hooks/post-merge $HUSKY_GIT_PARAMS", "pre-push": "echo $HUSKY_GIT_STDIN | lfs-hooks/pre-push $HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["biome check --unsafe"]}, "expo": {"autolinking": {"exclude": ["expo-keep-awake", "react-native-reanimated"]}}, "resolutions": {"react-native": "0.79.5"}, "packageManager": "yarn@3.6.4", "description": "### Get yourself added to these projects", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://*****************/fonos/mobile.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://bitbucket.org/fonos/mobile/issues"}, "homepage": "https://bitbucket.org/fonos/mobile#readme", "workspaces": ["functions"]}