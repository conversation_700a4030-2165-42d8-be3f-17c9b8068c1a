diff --git a/node_modules/react-native/ReactCommon/react/featureflags/ReactNativeFeatureFlags.cpp b/node_modules/react-native/ReactCommon/react/featureflags/ReactNativeFeatureFlags.cpp
index 992998a..8f9b01b 100644
--- a/node_modules/react-native/ReactCommon/react/featureflags/ReactNativeFeatureFlags.cpp
+++ b/node_modules/react-native/ReactCommon/react/featureflags/ReactNativeFeatureFlags.cpp
@@ -103,7 +103,7 @@ bool ReactNativeFeatureFlags::enableReportEventPaintTime() {
 }
 
 bool ReactNativeFeatureFlags::enableSynchronousStateUpdates() {
-  return getAccessor().enableSynchronousStateUpdates();
+  return true;
 }
 
 bool ReactNativeFeatureFlags::enableUIConsistency() {
diff --git a/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.cpp b/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.cpp
index 522ec57..92e8777 100644
--- a/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.cpp
+++ b/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.cpp
@@ -234,22 +234,17 @@ std::shared_ptr<const MountingCoordinator> ShadowTree::getMountingCoordinator()
 }
 
 CommitStatus ShadowTree::commit(
-    const ShadowTreeCommitTransaction& transaction,
-    const CommitOptions& commitOptions) const {
-  [[maybe_unused]] int attempts = 0;
-
-  while (true) {
-    attempts++;
-
-    auto status = tryCommit(transaction, commitOptions);
-    if (status != CommitStatus::Failed) {
-      return status;
-    }
-
-    // After multiple attempts, we failed to commit the transaction.
-    // Something internally went terribly wrong.
-    react_native_assert(attempts < 1024);
-  }
+  const ShadowTreeCommitTransaction& transaction,
+  const CommitOptions& commitOptions) const {
+  // WARNING: This is *not* production-ready code. Please use it wisely.
+  // For more details, see https://github.com/software-mansion/react-native-reanimated/issues/7460.
+  size_t myTicket = ticketCounter.fetch_add(1, std::memory_order_relaxed);
+  std::unique_lock<std::mutex> lock(mtx);
+  cv.wait(lock, [&]() { return serviceCounter.load(std::memory_order_relaxed) == myTicket; });
+  CommitStatus status = tryCommit(transaction, commitOptions);
+  serviceCounter.fetch_add(1, std::memory_order_relaxed);
+  cv.notify_all();
+  return status;
 }
 
 CommitStatus ShadowTree::tryCommit(
diff --git a/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.h b/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.h
index ae9d005..a63d230 100644
--- a/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.h
+++ b/node_modules/react-native/ReactCommon/react/renderer/mounting/ShadowTree.h
@@ -143,6 +143,11 @@ class ShadowTree final {
       CommitMode::Normal}; // Protected by `commitMutex_`.
   mutable ShadowTreeRevision currentRevision_; // Protected by `commitMutex_`.
   std::shared_ptr<const MountingCoordinator> mountingCoordinator_;
+
+  mutable std::mutex mtx;
+  mutable std::condition_variable cv;
+  mutable std::atomic<size_t> ticketCounter = 0;
+  mutable std::atomic<size_t> serviceCounter = 0;
 };
 
 } // namespace facebook::react
