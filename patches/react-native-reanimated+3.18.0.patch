diff --git a/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.h b/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.h
index 42a29b2..d359864 100644
--- a/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.h
+++ b/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.h
@@ -26,7 +26,7 @@ class PropsRegistry {
   void remove(const Tag tag);
 
   void pauseReanimatedCommits() {
-    isPaused_ = true;
+    // isPaused_ = true;
   }
 
   bool shouldReanimatedSkipCommit() {
