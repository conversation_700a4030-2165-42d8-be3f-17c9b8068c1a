import { useEffect, useState } from 'react'
import { Platform } from 'react-native'
import { KeyboardProvider } from 'react-native-keyboard-controller'
import {
  initialWindowMetrics,
  SafeAreaProvider,
} from 'react-native-safe-area-context'

import ReducedMotionConfig from 'Components/Animation/ReducedMotionConfig'
import Box from 'Components/Box'
import Button from 'Components/Button'
import { loadIconFont } from 'Components/Icon/Icon'
import { LoadingListener } from 'Components/Loading'
import ComponentProvider from 'Components/Provider'
import ThemeStatusBar from 'Components/Theme/ThemeStatusBar/ThemeStatusBar'
import useAppIcon from 'Services/AppIcon/Hooks/useAppIcon'
import Database from 'Services/Database'
import DatabaseConnectionListener from 'Services/Database/DatabaseConnection/DatabaseConnectionListener'
import DeepLinkProvider from 'Services/DeepLink/DeepLinkProvider'
import NetworkProvider from 'Services/Network/NetworkProvider'
import useNotificationService from 'Services/Notification/Hooks.ts/useNotificationService'
import QueryClientProvider from 'Services/QueryClient/QueryClientProvider'
import useAppSession from 'Hooks/useAppSession'
import useSplashScreen from 'Hooks/useSplashScreen'
import { testProps } from 'Utils/testHelpers'
import TranslationProvider from 'Translation/TranslationProvider'
import useExitPlayer from 'Player/Hooks/useExitPlayer'
import { Incremental, IncrementalGroup } from 'Libs/react-native-incremental'

import AppService from './AppService'
import MigrationRunner from './Migrations/MigrationRunner'
import RootComponent from './RootComponent'
import AppNavigator from './Screens'

loadIconFont()

const App = () => {
  useEffect(() => {
    // Workaround for receiving push notification on iOS, only init when windows mounted
    if (Platform.OS === 'ios') {
      Database.mobile()
      AppService.init()
    }
  }, [])

  // Workaround player not exiting on Android when killing app
  useExitPlayer()
  useAppSession()
  useAppIcon()
  useSplashScreen()
  useNotificationService()

  return (
    <>
      <ThemeStatusBar />
      <ReducedMotionConfig />
      <DatabaseConnectionListener />
      <MigrationRunner>
        <AppNavigator />
      </MigrationRunner>
      <LoadingListener />
      <IncrementalGroup name='app'>
        <Incremental>
          <RootComponent />
        </Incremental>
      </IncrementalGroup>
    </>
  )
}

const locale = 'vi'

const withProfilingEnabled = (Component: any, enabled: boolean = true) => {
  return () => {
    const [renderedApp, setRenderedApp] = useState(!enabled)
    useSplashScreen()

    return !renderedApp ? (
      <Box center flex1>
        <Button
          type='primary'
          label='Render App'
          onPress={() => setRenderedApp(!renderedApp)}
          {...testProps('render-app-button')}
        />
      </Box>
    ) : (
      <Component />
    )
  }
}

export default withProfilingEnabled(() => {
  return (
    <SafeAreaProvider
      {...(Platform.OS === 'android' && {
        initialMetrics: initialWindowMetrics,
      })}>
      <KeyboardProvider>
        <TranslationProvider locale={locale}>
          <QueryClientProvider>
            <NetworkProvider>
              <ComponentProvider>
                <DeepLinkProvider>
                  <App />
                </DeepLinkProvider>
              </ComponentProvider>
            </NetworkProvider>
          </QueryClientProvider>
        </TranslationProvider>
      </KeyboardProvider>
    </SafeAreaProvider>
  )
}, __DEV__ && false)
