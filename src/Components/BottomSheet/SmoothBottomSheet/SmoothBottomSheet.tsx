import { Platform, StyleSheet } from 'react-native'
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react'
import * as React from 'react'
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetFlatList,
  BottomSheetScrollView,
  BottomSheetView,
  useBottomSheetSpringConfigs,
} from '@gorhom/bottom-sheet'
import { invoke } from 'lodash'
import { Portal } from 'react-native-portalize'
import { ReduceMotion } from 'react-native-reanimated'

import useBottomInsets from 'Hooks/useBottomInsets'
import Dimensions from 'Constants/dimensions'
import BottomSheetHeader from '../Components/BottomSheetHeader'
import { DarkTheme, Theme, useThemeSelect } from 'Components/Theme'
import { useResponsiveLayout } from 'Components/Box/ResponsiveLayout'
import ConditionalWrapper from 'Components/ConditionWrapper'
import useTopInsets from 'Hooks/useTopInsets'

import type { SmoothBottomSheetProps } from './SmoothBottomSheet.d'

const CONTENT_WRAPPER = {
  view: BottomSheetView,
  scrollview: BottomSheetScrollView,
  flatlist: BottomSheetFlatList,
}

const SmoothBottomSheet = forwardRef(
  (
    {
      portalize = false,
      enableDynamicSizing = true,
      enableContentPanningGesture = true,
      children,
      wrapperType = 'view',
      withHandle = true,
      onClosed,
      title,
      titleSize,
      titleCenter,
      description,
      descriptionProps,
      renderHeaderContent,
      onOpened,
      onOpen,
      renderChildren,
      defaultOpened,
      renderContent,
      headerHidden,
      dismissible = true,
      modalStyle,
      theme: _overrideTheme = 'auto',
      snapPoints,
      keyboardBehavior,
      animateOnMount = false,
    }: React.PropsWithChildren<SmoothBottomSheetProps>,
    ref,
  ) => {
    const innerRef = useRef<BottomSheet>(null)
    const paddingBottom = useBottomInsets(16)
    const topInset = useTopInsets()
    const { spacingValue } = useResponsiveLayout()

    const theme = useThemeSelect(t =>
      _overrideTheme && _overrideTheme !== 'auto' ? _overrideTheme : t.mode,
    )

    const animationConfigs = useBottomSheetSpringConfigs({
      damping: 80,
      overshootClamping: true,
      restDisplacementThreshold: 0.1,
      restSpeedThreshold: 0.5,
      stiffness: 500,
      reduceMotion: ReduceMotion.Never,
    })

    const handleStyle = useMemo(
      () => ({
        backgroundColor:
          theme === 'dark' ? DarkTheme.colors.breakLine : '#DDE6F1',
        width: 39,
      }),
      [theme],
    )

    const ContentWrapper: any = useMemo(
      () => CONTENT_WRAPPER[wrapperType] || BottomSheetView,
      [wrapperType],
    )

    const contentWrapperProps = useMemo(() => {
      switch (wrapperType) {
        case 'scrollview':
          return {
            style: [
              { maxHeight: Dimensions.height * 0.9 - paddingBottom }, // this make content scrollable
            ],
            contentContainerStyle: [
              styles.content,
              withHandle && styles.withHandle,
              modalStyle,
            ],
          }
        default:
          return {
            style: [
              styles.content,
              withHandle && styles.withHandle,
              modalStyle,
            ],
          }
      }
    }, [modalStyle, paddingBottom, withHandle, wrapperType])

    const open = useCallback(() => {
      invoke(innerRef, 'current.expand')
    }, [])
    const close = useCallback(() => {
      invoke(innerRef, 'current.close')
    }, [])

    const onChange = useCallback(
      (index: number) => {
        if (index >= 0) {
          onOpened?.()
        } else {
          onClosed?.()
        }
      },
      [onClosed, onOpened],
    )

    const onAnimate = useCallback(
      (_fromIndex: number, toIndex: number) => {
        if (toIndex >= 0) onOpen?.()
      },
      [onOpen],
    )

    useImperativeHandle(
      ref,
      () => ({
        open,
        close,
      }),
      [close, open],
    )

    return (
      <ConditionalWrapper wrapper={Portal} condition={portalize}>
        <BottomSheet
          ref={innerRef}
          enablePanDownToClose={dismissible}
          topInset={Platform.select({
            ios: undefined,
            android: topInset,
          })}
          detached={true}
          bottomInset={paddingBottom}
          style={[{ marginHorizontal: spacingValue || 8 }]}
          enableDynamicSizing={enableDynamicSizing}
          snapPoints={
            !enableDynamicSizing ? (snapPoints ?? ['80%']) : undefined
          }
          enableContentPanningGesture={enableContentPanningGesture}
          enableHandlePanningGesture
          animationConfigs={animationConfigs}
          onAnimate={onAnimate}
          onChange={onChange}
          maxDynamicContentSize={Dimensions.height * 0.9}
          {...(!withHandle && { handleComponent: null })}
          handleIndicatorStyle={handleStyle}
          backgroundStyle={[styles.modal, theme === 'dark' && styles.darkMode]}
          index={defaultOpened ? undefined : -1}
          keyboardBehavior={keyboardBehavior}
          keyboardBlurBehavior="restore"
          accessible={false}
          accessibilityLabel={null}
          accessibilityRole={null}
          animateOnMount={animateOnMount}
          backdropComponent={props => (
            <BottomSheetBackdrop
              {...props}
              disappearsOnIndex={-1}
              appearsOnIndex={0}
              enableTouchThrough
              opacity={0.8}
              pressBehavior={dismissible ? 'close' : 'none'}
            />
          )}>
          {renderContent ? (
            renderContent()
          ) : (
            <ContentWrapper {...contentWrapperProps}>
              {renderChildren ? (
                renderChildren({ onClose: close })
              ) : headerHidden ? (
                children
              ) : (
                <React.Fragment>
                  <BottomSheetHeader
                    title={title}
                    titleSize={titleSize}
                    titleCenter={titleCenter}
                    description={description}
                    descriptionProps={descriptionProps}
                    renderHeaderContent={renderHeaderContent}
                    theme={theme}
                    {...(!withHandle && { onButtonClosePress: close })}
                  />
                  {children}
                </React.Fragment>
              )}
            </ContentWrapper>
          )}
        </BottomSheet>
      </ConditionalWrapper>
    )
  },
)

export default SmoothBottomSheet

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 8,
  },
  content: {
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  withHandle: {
    paddingTop: 0,
  },
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flex: 1,
  },
  modal: {
    borderRadius: 20,
    paddingTop: 18,
    paddingHorizontal: 24,
  },
  backdrop: { flex: 1 },
  close: {},
  darkMode: {
    backgroundColor: Theme.colors.blackGray,
  },
})
