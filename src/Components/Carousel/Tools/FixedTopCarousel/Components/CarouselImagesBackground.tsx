import { Platform, StyleSheet } from 'react-native'
import { memo } from 'react'
import Animated, {
  interpolateColor,
  type SharedValue,
  useAnimatedStyle,
  useDerivedValue,
} from 'react-native-reanimated'

import Dimensions from 'Constants/dimensions'
import { calcRealIndex } from '../../../utils'
import BlurView from 'Components/BlurView'
import GradientView from 'Components/GradientView'
import { PALLETS, Theme } from 'Components/Theme'

import StickyBackgroundWrapper, {
  type StickyBackgroundWrapperProps,
} from './StickyBackgroundWrapper'

const GRADIENT = {
  light: {
    colors: [
      'rgba(33, 43, 74, 1)',
      'rgba(99, 129, 166, 0.3)',
      'rgba(243,248,252, 0)',
      PALLETS.LIGHT_BLUE,
    ],
    start: { x: 0, y: -0.05 },
    end: { x: 0, y: 1 },
    locations: [0, 0.5, 0.98, 1] as [number, number, ...number[]],
  },
  dark: {
    colors: [
      'rgba(158, 206, 249, 0.1)',
      'rgba(99, 129, 166, 0.15)',
      PALLETS.DARK_BLACK_BLUE,
    ],
    start: { x: 0, y: -0.5 },
    end: { x: 0, y: 1 },
    locations: [0.3, 0.35, 1] as [number, number, ...number[]],
  },
}

interface CarouselImagesBackgroundProps extends StickyBackgroundWrapperProps {
  data?: { backgroundUrl: string; id: number; coverColor: string }[]
  animatedScrollOffsetValue: SharedValue<number>
  width: number
  customBackgroundBlurRadius?: number
  theme?: 'light' | 'dark'
}

const AnimatedBackground = ({
  animatedIndex,
  data,
}: {
  animatedIndex: SharedValue<number>
  data: string[]
}) => {
  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        animatedIndex.value,
        [
          ...new Array(data.length).fill(0).map((_, i) => i),
          data.length + 1, // Loop index run from n -> n+1 -> 0
        ],
        [...data.map(x => x), data[0]],
      ),
    }
  }, [data])

  return <Animated.View style={[styles.img, animatedStyle]} />
}

const CarouselImagesBackground = ({
  data = [],
  animatedScrollOffsetValue = { value: 0 } as SharedValue<number>,
  width = Dimensions.width,
  theme = 'light',
  ...props
}: CarouselImagesBackgroundProps) => {
  const animatedIndex = useDerivedValue(() => {
    return calcRealIndex(animatedScrollOffsetValue.value, data?.length, width)
  }, [data?.length, width])

  return (
    <StickyBackgroundWrapper {...props} theme={theme}>
      {data?.length ? (
        <AnimatedBackground
          animatedIndex={animatedIndex}
          data={data.map(x => x?.coverColor || Theme.colors.light)}
        />
      ) : null}
      {Platform.OS === 'ios' && (
        <BlurView
          style={StyleSheet.absoluteFill}
          blurAmount={15}
          blurType={theme === 'dark' ? 'dark' : 'light'}
        />
      )}

      <GradientView {...GRADIENT[theme]} style={styles.darkOverlay} />
    </StickyBackgroundWrapper>
  )
}

export default memo(CarouselImagesBackground)

const styles = StyleSheet.create({
  img: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -3,
  },
  darkOverlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
})
