import { StyleSheet } from 'react-native'
import { useMemo } from 'react'
import { chunk } from 'lodash'

import { COURSE_RATIO } from 'Components/Course/CourseCover/constants'
import Box from 'Components/Box'
import { Theme } from 'Components/Theme'

import useCollectionStyles from './Hooks/useCollectionStyles'
import CourseWithInfoItemSkeleton from './Components/CourseWithInfoItem.skeleton'

const DEFAULT_NUMBER_OF_ROWS_COLLECTION = 3
const ITEM_SPACING = 'sm'

const ARRAY = Array.from({ length: 6 }, (_, i) => i)

interface CourseCollectionSkeletonProps {
  numRows?: number
}
const CourseCollectionSkeleton = ({
  numRows = DEFAULT_NUMBER_OF_ROWS_COLLECTION,
}: CourseCollectionSkeletonProps) => {
  const { itemWidth, columnHeight, itemHeight } = useCollectionStyles({
    numberOfRows: numRows,
    ratio: COURSE_RATIO,
    itemSpacing: ITEM_SPACING,
  })
  const arr = useMemo(() => chunk(ARRAY, numRows), [numRows])

  return (
    <Box row style={styles.contentContainerStyle} spacing="sm">
      {arr.map((item, columnIndex) => (
        <Box
          key={columnIndex}
          style={[
            {
              width: itemWidth,
              height: columnHeight,
            },
            styles.item,
          ]}
          spacing="sm">
          {item.map((_, itemIndex) => (
            <Box key={itemIndex} style={[{ height: itemHeight }]}>
              <CourseWithInfoItemSkeleton />
            </Box>
          ))}
        </Box>
      ))}
    </Box>
  )
}

export default CourseCollectionSkeleton

const styles = StyleSheet.create({
  contentContainerStyle: {
    paddingHorizontal: Theme.spacing.m,
  },
  item: {
    overflow: 'hidden',
  },
})
