import { StyleSheet } from 'react-native'
import { useMemo } from 'react'

import { COURSE_RATIO } from 'Components/Course/CourseCover/constants'
import Box from 'Components/Box'
import { Theme } from 'Components/Theme'

import CourseWithInfoItemSkeleton from './Components/CourseWithInfoItem.skeleton'

const DEFAULT_NUMBER_OF_ROWS_COLLECTION = 3

const HEIGHT = 88 / COURSE_RATIO

interface CourseVerticalSectionListSkeletonProps {
  numRows?: number
}
const CourseVerticalSectionListSkeleton = ({
  numRows = DEFAULT_NUMBER_OF_ROWS_COLLECTION,
}: CourseVerticalSectionListSkeletonProps) => {
  const arr = useMemo(
    () => Array.from({ length: numRows }, (_, i) => i),
    [numRows],
  )

  return (
    <Box style={styles.contentContainerStyle}>
      {arr.map((_, index) => (
        <Box key={index} style={styles.item} marginBottom="sm">
          <CourseWithInfoItemSkeleton />
        </Box>
      ))}
    </Box>
  )
}

export default CourseVerticalSectionListSkeleton

const styles = StyleSheet.create({
  contentContainerStyle: {
    paddingHorizontal: Theme.spacing.m,
  },
  item: {
    height: HEIGHT,
  },
})
