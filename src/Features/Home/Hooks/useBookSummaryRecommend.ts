import { first } from 'lodash'

import { BookSummaryService } from 'Repo/applications/bookSummary'
import { MediaType } from 'Repo/constants/media'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import { useHasAuthenticated } from 'Features/Auth/Stores'
import { updateHomeBookColor } from 'Features/HomeBook/Store/HomeBookStore'

const useBookSummaryRecommend = () => {
  const hasAuthenticated = useHasAuthenticated()

  return useRequestQuery(
    ['useBookSummaryRecommend'],
    async () => {
      if (!hasAuthenticated) return []

      const { items } = await BookSummaryService.getRecommendationBookSummaries(
        {
          limit: 10,
        },
      )

      const firstItem = first(items)
      if (firstItem)
        updateHomeBookColor(MediaType.BOOK_SUMMARY, firstItem.coverColor)
      return items
    },
    {
      suspense: true,
    },
  )
}
export default useBookSummaryRecommend
