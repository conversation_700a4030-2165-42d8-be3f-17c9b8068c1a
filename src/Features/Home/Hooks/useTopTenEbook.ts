import { first } from 'lodash'

import { EbookService } from 'Repo/applications/ebook'
import { MediaType } from 'Repo/constants/media'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import { updateHomeBookColor } from 'Features/HomeBook/Store/HomeBookStore'

export const TopTenEbookQueryConfig = {
  queryKey: ['topTenEbooks'],
  queryFn: async () => {
    const { items } = await EbookService.getTrendingEbooks({ limit: 10 })
    const firstItem = first(items)
    if (firstItem) updateHomeBookColor(MediaType.EBOOK, firstItem.coverColor)
    return items
  },
}

const useTopTenEbook = () => {
  return useRequestQuery(
    TopTenEbookQueryConfig.queryKey,
    TopTenEbookQueryConfig.queryFn,
    {
      suspense: true,
      cacheForHours: 24,
    },
  )
}
export default useTopTenEbook
