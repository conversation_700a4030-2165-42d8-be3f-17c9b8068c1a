import { memo, useMemo } from 'react'
import { type StyleProp, StyleSheet, type ViewStyle } from 'react-native'
import Animated, {
  Easing,
  useAnimatedStyle,
  useDerivedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated'

import { BOOK_RATIO } from 'Components/Book/BookCover/constants'
import Box from 'Components/Box'
import ScaleTouchable from 'Components/ScaleTouchable'
import { accessibilityProps } from 'Services/Accessibility'
import { testProps } from 'Utils/testHelpers'

import {
  BOOK_CAROUSEL_HEIGHT,
  BOOK_CAROUSEL_RATIO,
  BOOK_CAROUSEL_TAG_HEIGHT,
} from './constants'
import { useBookCarouselAnimatedIndex } from './Store/BookCarouselStore'

const SPRING_CONFIG = {
  mass: 2,
  damping: 80,
  stiffness: 500,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 100,
}
const TIMING_CONFIG = {
  duration: 200,
  easing: Easing.out(Easing.ease),
}

interface BookAnimatedCarouselLayoutProps {
  CoverComponent: React.ReactNode
  ContentComponent: React.ReactNode
  ActionButtonComponent: React.ReactNode
  borderRadius: number
  contentHeight: number
  onPress: () => void
  index: number
  actionButtonStyle?: StyleProp<ViewStyle>
  accessibilityObject?: any
}

const BookAnimatedCarouselLayout = ({
  CoverComponent,
  ContentComponent,
  ActionButtonComponent,
  borderRadius,
  contentHeight,
  onPress,
  index,
  actionButtonStyle,
  accessibilityObject,
}: BookAnimatedCarouselLayoutProps) => {
  const animatedIndex = useBookCarouselAnimatedIndex()
  const isSharedActive = useDerivedValue(() => animatedIndex.value === index)

  const springTranslateY = useDerivedValue(
    () => withSpring(isSharedActive.value ? -contentHeight : 0, SPRING_CONFIG),
    [contentHeight],
  )

  const animatedBRadius = useDerivedValue(
    () => withTiming(isSharedActive.value ? 0 : borderRadius, TIMING_CONFIG),
    [borderRadius],
  )

  const animatedCoverStyle = useAnimatedStyle(() => {
    return {
      borderBottomRightRadius: animatedBRadius.value,
      borderBottomLeftRadius: animatedBRadius.value,
      transform: [{ translateY: springTranslateY.value }],
    }
  })

  const springContentTranslateY = useDerivedValue(
    () => withSpring(isSharedActive.value ? 0 : -contentHeight, SPRING_CONFIG),
    [contentHeight],
  )

  const animatedContentStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: springContentTranslateY.value }],
    }
  })

  const animatedOpacity = useDerivedValue(() =>
    withTiming(isSharedActive.value ? 1 : 0, TIMING_CONFIG),
  )

  const animatedBtnStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedOpacity.value,
    }
  })

  const testId = useMemo(() => `__CAROUSEL_ITEM_${index}_READY__`, [index])

  const coverContainerStyle = useMemo(
    () => [styles.cover, { borderRadius }, animatedCoverStyle],
    [borderRadius, animatedCoverStyle],
  )
  const contentContainerStyle = useMemo(
    () => [
      styles.contentWrapper,
      { height: contentHeight },
      animatedContentStyle,
    ],
    [contentHeight, animatedContentStyle],
  )

  return (
    <Box style={styles.container}>
      <ScaleTouchable
        style={styles.container}
        onPress={onPress}
        {...testProps(testId)}
        {...accessibilityProps(accessibilityObject)}>
        <Animated.View style={coverContainerStyle}>
          {CoverComponent}
        </Animated.View>

        <Animated.View style={contentContainerStyle}>
          {ContentComponent}
        </Animated.View>
      </ScaleTouchable>
      <Animated.View style={[animatedBtnStyle, actionButtonStyle]}>
        {ActionButtonComponent}
      </Animated.View>
    </Box>
  )
}

const BookAnimatedCarouselLayoutMemo = memo(BookAnimatedCarouselLayout)

export default BookAnimatedCarouselLayoutMemo

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    height: BOOK_CAROUSEL_HEIGHT / BOOK_CAROUSEL_RATIO,
    justifyContent: 'flex-end',
  },
  cover: {
    overflow: 'hidden',
    aspectRatio: BOOK_RATIO,
    width: '100%',
  },
  contentWrapper: {
    zIndex: -1,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
  },
})
