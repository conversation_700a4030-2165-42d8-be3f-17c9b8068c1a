import { round } from 'lodash'
import { useCallback } from 'react'
import { useSharedValue } from 'react-native-reanimated'

import Box from 'Components/Box'
import ParallaxCarousel from 'Components/Carousel/ParallaxCarousel'
import { MediaType } from 'Repo/constants/media'
import type { PreConstructedSection } from 'Repo/entities/home-segment'
import { updateHomeBookColor } from 'Features/HomeBook/Store/HomeBookStore'
import Dimensions from 'Constants/dimensions'

import BookCarouselItem from './BookCarouselItem'
import { BOOK_CAROUSEL_HEIGHT, BOOK_CAROUSEL_RATIO } from './constants'
import BookCarouselStore from './Store/BookCarouselStore'

interface BookCarouselProps {
  data: PreConstructedSection
}
const BookCarousel = ({ data }: BookCarouselProps) => {
  const items = data.items || []
  const animatedScrollOffsetValue = useSharedValue(0)
  const renderItem = useCallback(({ item, index }: any) => {
    return <BookCarouselItem item={item} index={index} />
  }, [])
  const colors = items?.map(item => item?.coverColor || '#000000')

  const onProgressChange = useCallback(
    (_offsetProgress: number, absoluteProgress: number) => {
      const rounded = round(absoluteProgress)
      const index = rounded > (colors?.length || 0) - 1 ? 0 : rounded

      updateHomeBookColor(MediaType.BOOK, colors?.[index] || '#000000')
      BookCarouselStore.get().animatedIndex.value = index
    },
    [colors],
  )

  return (
    <Box paddingBottom="xxl">
      <ParallaxCarousel
        data={items}
        height={BOOK_CAROUSEL_HEIGHT}
        scale={BOOK_CAROUSEL_RATIO}
        width={Dimensions.width}
        renderItem={renderItem}
        inScrollView
        animatedScrollOffsetValue={animatedScrollOffsetValue}
        onProgressChange={onProgressChange}
        // windowSize={Platform.select({ ios: 0, default: 5 })}
      />
    </Box>
  )
}

export default BookCarousel
