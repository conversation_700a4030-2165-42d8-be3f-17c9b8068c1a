import { memo, useCallback, useMemo } from 'react'
import { Platform, StyleSheet } from 'react-native'

import BookCover from 'Components/Book/BookCover'
import Box from 'Components/Box'
import GradientView from 'Components/GradientView'
import { MediaType } from 'Repo/constants/media'
import type { Book } from 'Repo/entities/book'
import {
  accessibilityWithText,
  disableAccessibilityIncludeChildren,
} from 'Services/Accessibility'
import { ACCESSIBILITY_GENERAL_LABEL } from 'Services/Accessibility/constants'
import { PLAY_BOOK_CLICK_SOURCE } from 'Services/PlayMedia/constants'
import PlayMediaService from 'Services/PlayMedia/PlayMediaService'
import BLoC from 'Features/BLoC'
import { goToDetailPageByMediaType } from 'Features/BLoC/Hooks/useGoDetailPage'
import useMediaPlayableRules from 'Features/BLoC/Hooks/useMediaPlayableRules'
import type { HomeCarouselItemType } from 'Features/HomeCarousel/constants'
import { mixColor } from 'Features/PlayerModal/Utils'
import PurchaseSourceStore from 'Features/Purchase/Stores/PurchaseSourceStore'
import { responsive } from 'Utils/responsive'
import Analytics from 'Trackings/Analytics'
import PlayButton from 'Player/Components/PlayButton'

import BookAnimatedCarouselLayout from './BookAnimatedCarouselLayout'
import BookCarouselTag from './BookCarouselTag'
import { BOOK_CAROUSEL_TAG_HEIGHT, getCarouselScaleFontSize } from './constants'

const BORDER_RADIUS = responsive(16)
const BUTTON_SIZE = getCarouselScaleFontSize(36)

interface ContentProps {
  data: Book & {
    itemType: HomeCarouselItemType
    coverColor?: string
  }
}

const Content = memo(({ data }: ContentProps) => {
  const colors = useMemo(
    () => (data?.coverColor ? [mixColor(data?.coverColor).lighten] : []),
    [data?.coverColor],
  )

  const textColor = useMemo(
    () => mixColor(data?.coverColor).darken,
    [data?.coverColor],
  )

  return (
    <Box
      center
      style={[styles.content]}
      {...disableAccessibilityIncludeChildren()}>
      <GradientView colors={colors} style={StyleSheet.absoluteFill} />
      <BookCarouselTag {...data} textColor={textColor} />
    </Box>
  )
})

interface PlayActionButtonProps {
  data: Book & {
    itemType: HomeCarouselItemType
    coverColor?: string
  }
}

const PlayActionButton = memo(({ data }: PlayActionButtonProps) => {
  const { id, title, coverColor, coverImageUrl, itemType } = data || {}

  const onPress = useCallback(() => {
    Analytics.playCarouselBook({ itemId: id, type: itemType })
    PlayMediaService.playBook({
      id,
      title,
      coverColor,
      coverImageUrl,
      clickSource: PLAY_BOOK_CLICK_SOURCE.CAROUSEL,
    })
  }, [coverColor, coverImageUrl, id, itemType, title])

  const playRules = useMediaPlayableRules(MediaType.BOOK, data)

  const iconSize = useMemo(() => getCarouselScaleFontSize(12), [])

  return (
    <BLoC.ScaleTouchable
      {...playRules}
      style={[styles.btnWrapper]}
      onPress={onPress}
      {...accessibilityWithText(
        ACCESSIBILITY_GENERAL_LABEL.PLAY_BUTTON_CAROUSEL_BOOK_ITEM,
      )}>
      <PlayButton style={styles.btn} iconSize={iconSize} />
    </BLoC.ScaleTouchable>
  )
})

interface BookCarouselItemProps {
  item: Book & {
    itemType: HomeCarouselItemType
  }
  index: number
}

const BookCarouselItem = ({ item, index }: BookCarouselItemProps) => {
  const onPress = useCallback(() => {
    Analytics.clickCarouselBook(item?.id, {
      index,
      type: item?.itemType,
    })

    PurchaseSourceStore.get().setSource('Carousel books', {
      index,
      type: item?.itemType,
    })
    return goToDetailPageByMediaType(MediaType.BOOK, {
      ...item,
      clickSource: 'audiobook-carousel',
    })
  }, [index, item])

  const coverComponent = useMemo(
    () => (
      <Box {...disableAccessibilityIncludeChildren()}>
        <BookCover
          size="auto"
          {...item}
          round={0}
          shadowType="darker"
          useRNGH={Platform.OS === 'ios'} // fix tap issue on swipe
          url={item.coverImageUrl}
          fallbackLowerRatio
          priority={'high'}
          style={styles.bookCover}
        />
      </Box>
    ),
    [item],
  )

  const contentComponent = useMemo(() => <Content data={item} />, [item])

  const actionButtonComponent = useMemo(
    () => <PlayActionButton data={item} />,
    [item],
  )

  const accessibilityObject = useMemo(
    () => ({
      label: ACCESSIBILITY_GENERAL_LABEL.BOOK_COVER + item?.title || '',
    }),
    [item?.title],
  )

  return (
    <BookAnimatedCarouselLayout
      CoverComponent={coverComponent}
      ContentComponent={contentComponent}
      ActionButtonComponent={actionButtonComponent}
      contentHeight={BOOK_CAROUSEL_TAG_HEIGHT}
      borderRadius={BORDER_RADIUS}
      onPress={onPress}
      index={index}
      accessibilityObject={accessibilityObject}
    />
  )
}

export default memo(BookCarouselItem)

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: getCarouselScaleFontSize(12),
    borderBottomRightRadius: BORDER_RADIUS,
    borderBottomLeftRadius: BORDER_RADIUS,
    overflow: 'hidden',
    height: '100%',
  },

  btnWrapper: {
    position: 'absolute',
    right: getCarouselScaleFontSize(12),
    bottom: BOOK_CAROUSEL_TAG_HEIGHT - BUTTON_SIZE / 2,
  },
  btn: {
    width: BUTTON_SIZE,
    aspectRatio: 1,
    borderRadius: BUTTON_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bookCover: {
    marginTop: -3,
  },
})
