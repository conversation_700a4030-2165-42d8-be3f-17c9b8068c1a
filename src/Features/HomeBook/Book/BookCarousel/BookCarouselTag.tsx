import { memo, useMemo } from 'react'
import { useIntl } from 'react-intl'
import { StyleSheet } from 'react-native'

import Box, { type BoxProps } from 'Components/Box'
import Icon from 'Components/Icon'
import Text from 'Components/Text'
import { lineHeight, PALLETS, Theme } from 'Components/Theme'
import { HomeCarouselItemType } from 'Features/HomeCarousel/constants'
import messages from 'Features/HomeCarousel/messages'

import { BOOK_CAROUSEL_TAG_HEIGHT, getCarouselScaleFontSize } from './constants'

interface BookCarouselTagProps extends BoxProps {
  itemType: HomeCarouselItemType
  topTrendingIndex?: number
  textColor?: string
}

const BookCarouselTag = ({
  itemType,
  topTrendingIndex,
  textColor = PALLETS.WHITE,
  ...boxProps
}: BookCarouselTagProps) => {
  const intl = useIntl()

  const { label, tagIconChildren } = useMemo(() => {
    switch (itemType) {
      case HomeCarouselItemType.NewlyReleased:
        return {
          label: messages.newlyReleased,
          tagIconChildren: (
            <Icon
              name="spark"
              size={getCarouselScaleFontSize(14)}
              color={textColor}
            />
          ),
        }
      case HomeCarouselItemType.TopTrending:
        return {
          label: messages.topTrending,
          tagIconChildren: (
            <Text style={[styles.txt, { color: textColor }]}>
              #{topTrendingIndex}
            </Text>
          ),
        }

      case HomeCarouselItemType.LibraryIncluded:
        return {
          label: messages.libraryIncluded,
          tagIconChildren: (
            <Icon
              name="library"
              size={getCarouselScaleFontSize(14)}
              color={textColor}
            />
          ),
        }

      default:
        return {}
    }
  }, [itemType, textColor, topTrendingIndex])

  return (
    <Box row alignItemsCenter style={styles.container} {...boxProps}>
      {tagIconChildren}

      <Text marginLeft="s" style={[styles.txt, { color: textColor }]}>
        {label ? intl.formatMessage(label) : ''}
      </Text>
    </Box>
  )
}

export default memo(BookCarouselTag)

const styles = StyleSheet.create({
  container: {
    height: BOOK_CAROUSEL_TAG_HEIGHT,
  },
  txt: {
    fontSize: getCarouselScaleFontSize(Theme.textVariants.normal.fontSize),
    lineHeight: lineHeight(getCarouselScaleFontSize(24)),
    letterSpacing: 0.02,
  },
})
