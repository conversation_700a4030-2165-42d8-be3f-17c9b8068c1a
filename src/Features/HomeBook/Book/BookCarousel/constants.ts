import { BO<PERSON>_RATIO } from 'Components/Book/BookCover/constants'
import { responsive } from 'Utils/responsive'

const ITEM_WIDTH = responsive(190, true)
export const BOOK_CAROUSEL_RATIO = 0.5
export const BO<PERSON>_CAROUSEL_TAG_HEIGHT = 60
export const BOOK_CAROUSEL_HEIGHT =
  ITEM_WIDTH / BOOK_RATIO + BOOK_CAROUSEL_TAG_HEIGHT
console.log('🚀 ~ BOOK_CAROUSEL_HEIGHT:', BOOK_CAROUSEL_HEIGHT)
export const getCarouselScaleFontSize = (size: number) =>
  size * (1.4 + BOOK_CAROUSEL_RATIO)
