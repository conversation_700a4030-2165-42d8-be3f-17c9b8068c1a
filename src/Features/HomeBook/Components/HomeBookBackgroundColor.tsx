import type React from 'react'
import { memo } from 'react'
import { StyleSheet } from 'react-native'
import type { SharedValue } from 'react-native-reanimated'

import GradientView from 'Components/GradientView'
import InterpolateView from 'Components/Header/InterpolateView'
import { useThemeSelect } from 'Components/Theme'
import type { MediaType } from 'Repo/constants/media'
import { getDarkColor, getLightColor } from 'Features/Book/utils'

import { useHomeBookColor } from '../Store/HomeBookStore'

const getColor = (color?: any) => {
  if (!color) {
    return {
      light: ['rgba(243, 248, 252, 1)', 'rgba(211, 210, 247, 1)'],
      dark: ['rgba(26, 28, 32, 1)', 'rgba(69, 41, 112, 1)'],
    }
  }

  return {
    light: ['rgba(243, 248, 252, 1)', getLightColor(color || '#000')],
    dark: ['rgba(26, 28, 32, 1)', getDarkColor(color || '#000')],
  }
}

interface HomeBookBackgroundColorProps {
  scrollY: SharedValue<number>
  activeTab?: MediaType
}
const HomeBookBackgroundColor: React.FC<HomeBookBackgroundColorProps> = ({
  scrollY,
  activeTab,
}) => {
  const color = useHomeBookColor(activeTab as any)

  const backgroundColors = useThemeSelect(getColor(color) as any)

  return (
    <InterpolateView scrollY={scrollY} position="absolute" zIndex={-1}>
      <GradientView
        style={[StyleSheet.absoluteFillObject, { zIndex: 999 }]}
        colors={backgroundColors}
        start={{ x: 0, y: 1 }}
        end={{ x: 0, y: 0.2 }}
      />
    </InterpolateView>
  )
}

export default memo(HomeBookBackgroundColor)
