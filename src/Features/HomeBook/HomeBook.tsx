import { useCallback, useMemo, useRef, useState } from 'react'
import { StyleSheet } from 'react-native'
import {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated'

import Box from 'Components/Box'
import List from 'Components/List'
import { StickyHeader } from 'Components/StickyHeader'
import { Theme } from 'Components/Theme'
import StatusBar from 'Components/Theme/ThemeStatusBar/StatusBar'
import { MediaType } from 'Repo/constants/media'
import type { PreConstructedSection } from 'Repo/entities/home-segment'
import AppHeader, { FULL_APP_HEADER_HEIGHT } from 'Features/AppHeader'
import useHomeConfigurationLayout from 'Features/Home/Hooks/useHomeConfigurationLayout'
import useScrollToTop from 'Hooks/useScrollToTop'
import useTopInsets from 'Hooks/useTopInsets'

import HomeBookBackgroundColor from './Components/HomeBookBackgroundColor'
import HomeBookGradientBackground from './Components/HomeBookGradientBackground'
import Tabs, { TAB_HEIGHT } from './Components/Tabs'
import { getEstimatedItemSize } from './constants'
import HomeBookItem from './HomeBookItem'
import QueryOrchestrator from './Query/QueryOrchestrator'

const HomeBook = () => {
  const listRef = useRef<any>(null)
  const topInset = useTopInsets()
  const scrollY = useSharedValue(0)
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollY.value = event.contentOffset.y
    },
  })
  const paddingTop = useMemo(
    () => FULL_APP_HEADER_HEIGHT + topInset + TAB_HEIGHT + Theme.spacing.lr,
    [topInset],
  )

  const [activeTab, setActiveTab] = useState(MediaType.BOOK)

  const { data, refetch, isLoading } = QueryOrchestrator.useQueryData(activeTab)

  const { data: oldConfig, isLoading: isOldConfigLoading } =
    useHomeConfigurationLayout()

  const keyExtractor = (item: PreConstructedSection) => {
    return `${item.id}_${item.key}_${item.displayType}`
  }
  const renderItem = ({ item }: { item: PreConstructedSection }) => {
    try {
      return (
        <HomeBookItem
          data={item}
          type={activeTab}
          oldConfig={oldConfig}
          isOldConfigLoading={isOldConfigLoading}
        />
      )
    } catch (error) {
      console.warn('Error rendering HomeBookItem:', item.key, error)
      return null
    }
  }

  const scrollToTop = useCallback((animated = true) => {
    listRef.current?.scrollToOffset?.({
      animated,
    })
  }, [])

  useScrollToTop(scrollToTop)

  return (
    <Box>
      <StatusBar style="reverse" />
      <StickyHeader
        HiddenOnStickedComponent={<AppHeader logoColor />}
        ShownOnStickedComponent={<HomeBookGradientBackground />}
        scrollY={scrollY}
        stickyTopOffset={topInset}>
        <Tabs activeTab={activeTab} onSelect={setActiveTab} />
      </StickyHeader>
      <HomeBookBackgroundColor scrollY={scrollY} activeTab={activeTab} />

      <List
        ref={listRef}
        avoidingBottom
        useFlatList
        recycleItems={true}
        drawDistance={500}
        waitForInitialLayout
        getEstimatedItemSize={getEstimatedItemSize}
        keyExtractor={keyExtractor}
        data={data || []}
        renderItem={renderItem}
        estimatedItemSize={80}
        contentContainerStyle={{ paddingTop }}
        onScroll={scrollHandler}
        style={styles.container}
        onRefresh={refetch}
        isLoading={isLoading}
        separator={null}
        avoidingBottomProps={{
          extendedValue: Theme.spacing.l,
        }}
      />
    </Box>
  )
}

export default HomeBook

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
  },
})
