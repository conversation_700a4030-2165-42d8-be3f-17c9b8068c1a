import { MediaType } from 'Repo/constants/media'
import type { PreConstructedSection } from 'Repo/entities/home-segment'
import QueryClient from 'Services/QueryClient'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import { getCurrentUserId } from 'Features/Auth/Stores'

import queryBookSections from '../Book/queryBookSections'
import queryBookSummarySections from '../BookSummary/queryBookSummarySections'
import queryEbookSections from '../Ebook/queryEbookSections'

const KEY = 'home_book_query'

const FUNC: any = {
  [MediaType.BOOK]: queryBookSections,
  [MediaType.EBOOK]: queryEbookSections,
  [MediaType.BOOK_SUMMARY]: queryBookSummarySections,
}

// Main orchestrator functions
const QueryOrchestrator = {
  clearCache: () => {
    QueryClient._invalidateQueries({ queryKey: [KEY, getCurrentUserId()] })
  },
  getQueryKey: (tab: MediaType) =>
    [KEY, getCurrentUserId(), tab].filter(Boolean),

  useQueryData: (tab: MediaType) => {
    const queryFn = FUNC[tab] || (() => [])

    return useRequestQuery<PreConstructedSection[]>(
      QueryOrchestrator.getQueryKey(tab),
      async () => {
        const response = await queryFn({})
        return response || []
      },
      {
        cacheForHours: 12,
        staleTime: 0,
      },
    )
  },
}

export default QueryOrchestrator
