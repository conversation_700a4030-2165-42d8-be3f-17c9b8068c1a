import { MediaType } from 'Repo/constants/media'
import Store from 'Services/Store'

interface HomeBookStoreState {
  [MediaType.BOOK]: { color: any }
  [MediaType.EBOOK]: { color: any }
  [MediaType.BOOK_SUMMARY]: { color: any }
}

const HomeBookStore = new Store<HomeBookStoreState>(() => ({
  [MediaType.BOOK]: { color: undefined },
  [MediaType.EBOOK]: { color: undefined },
  [MediaType.BOOK_SUMMARY]: { color: undefined },
}))

export default HomeBookStore

export const useHomeBookColor = (
  tab: MediaType.BOOK | MediaType.EBOOK | MediaType.BOOK_SUMMARY,
) => HomeBookStore.shallow(state => state[tab]?.color)

export const updateHomeBookColor = (
  tab: MediaType.BOOK | MediaType.EBOOK | MediaType.BOOK_SUMMARY,
  color: any,
) => {
  HomeBookStore.set(state => ({
    ...state,
    [tab]: {
      ...state[tab],
      color,
    },
  }))
}
