import type React from 'react'
import { memo } from 'react'
import { StyleSheet } from 'react-native'
import type { SharedValue } from 'react-native-reanimated'

import GradientView from 'Components/GradientView'
import InterpolateView from 'Components/Header/InterpolateView'
import { getDarkColor } from 'Features/Book/utils'

import { HomeCourseTab } from '../constants'
import { useHomeCourseColor } from '../Store/HomeCourseStore'

const _getColor = (color?: any) => {
  if (!color) return ['rgba(26, 28, 32, 1)', 'rgba(69, 41, 112, 1)']
  return ['rgba(26, 28, 32, 1)', getDarkColor(color || '#000')]
}

interface HomeCourseBackgroundColorProps {
  scrollY: SharedValue<number>
  activeTab?: HomeCourseTab
}

const HomeCourseBackgroundColor: React.FC<HomeCourseBackgroundColorProps> = ({
  scrollY,
  activeTab,
}) => {
  const color = useHomeCourseColor(activeTab || HomeCourseTab.COURSE)
  const backgroundColors = _getColor(color)

  return (
    <InterpolateView scrollY={scrollY} position="absolute" zIndex={-1}>
      <GradientView
        style={[StyleSheet.absoluteFillObject, { zIndex: 999 }]}
        colors={backgroundColors}
        start={{ x: 0, y: 0.5 }}
        end={{ x: 0, y: 0.1 }}
      />
    </InterpolateView>
  )
}

export default memo(HomeCourseBackgroundColor)
