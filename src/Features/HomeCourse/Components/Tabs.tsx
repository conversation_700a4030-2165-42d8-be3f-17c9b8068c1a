import type React from 'react'
import { useCallback } from 'react'
import {
  type DimensionValue,
  Pressable,
  StyleSheet,
  type ViewStyle,
} from 'react-native'

import Box from 'Components/Box'
import Text from 'Components/Text'
import { PALLETS, Theme, useThemeColor } from 'Components/Theme'

import { HomeCourseTab } from '../constants'

const TAB_ORDER = [HomeCourseTab.COURSE, HomeCourseTab.MENTOR] as const

const HOME_COURSE_TAB_LABELS = {
  [HomeCourseTab.COURSE]: 'Khóa học',
  [HomeCourseTab.MENTOR]: 'Mentor',
} as const

interface TabsProps {
  onSelect: (selectedTab: HomeCourseTab) => void
  activeTab?: HomeCourseTab
  style?: ViewStyle
  activeColor?: string
  inactiveTextColor?: string
  activeTextColor?: string
  containerBackgroundColor?: string // This will be defaulted to transparent
  underlineWidth?: DimensionValue
}

const Tabs: React.FC<TabsProps> = ({
  onSelect,
  activeTab = HomeCourseTab.COURSE,
  style,
  activeColor: activeColorProp,
  inactiveTextColor: inactiveTextColorProp,
  activeTextColor: activeTextColorProp,
}) => {
  const primaryColor = useThemeColor('primary')
  const defaultInactiveTextColor = useThemeColor('subText', 'tabMode')
  const defaultActiveTextColor = useThemeColor('mainText', 'tabMode')

  const resolvedActiveColor = activeColorProp || primaryColor
  const resolvedInactiveTextColor =
    inactiveTextColorProp || defaultInactiveTextColor
  const resolvedActiveTextColor = activeTextColorProp || defaultActiveTextColor

  const handlePress = useCallback(
    (tab: HomeCourseTab) => {
      if (activeTab === tab) return // Do nothing if already active
      onSelect(tab)
    },
    [activeTab, onSelect],
  )

  return (
    <>
      <Box style={[styles.container, style]}>
        {TAB_ORDER.map(tab => {
          const isActive = tab === activeTab
          const tabLabel = HOME_COURSE_TAB_LABELS[tab]
          return (
            <Pressable
              key={tab}
              style={styles.optionButton}
              onPress={() => handlePress(tab)}>
              <Text
                paddingBottom="s"
                style={[
                  {
                    color: isActive
                      ? resolvedActiveTextColor
                      : resolvedInactiveTextColor,
                  },
                ]}
                type="large"
                bold={isActive}>
                {tabLabel}
              </Text>
              {isActive && (
                <Box
                  style={[
                    styles.underline,
                    {
                      backgroundColor: resolvedActiveColor,
                    },
                  ]}
                />
              )}
            </Pressable>
          )
        })}
      </Box>
      <Box style={styles.separateLine} />
    </>
  )
}

export const TAB_HEIGHT = 40

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'stretch',
    height: TAB_HEIGHT,
    paddingHorizontal: Theme.spacing.m,
  },
  optionButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    position: 'relative',
    // paddingHorizontal: Theme.spacing.s,
  },
  underline: {
    position: 'absolute',
    bottom: 0,
    height: 3,
    right: 0,
    left: 0,
    borderRadius: 1.5,
    width: '100%',
  },
  separateLine: {
    width: '100%',
    height: 0.5,
    backgroundColor: PALLETS.WHITE_OPACITY(0.2),
  },
})

export default Tabs
