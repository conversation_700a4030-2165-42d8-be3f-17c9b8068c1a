import { useCallback, useMemo, useRef, useState } from 'react'
import { StyleSheet } from 'react-native'
import {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated'

import Box from 'Components/Box'
import List from 'Components/List'
import { StickyHeader } from 'Components/StickyHeader'
import { Theme } from 'Components/Theme'
import StatusBar from 'Components/Theme/ThemeStatusBar/StatusBar'
import type { PreConstructedSection } from 'Repo/entities/home-segment'
import AppHeader, { FULL_APP_HEADER_HEIGHT } from 'Features/AppHeader'
import useScrollToTop from 'Hooks/useScrollToTop'
import useTopInsets from 'Hooks/useTopInsets'

import HomeCourseBackgroundColor from './Components/HomeCourseBackgroundColor'
import HomeCourseGradientBackground from './Components/HomeCourseGradientBackground'
import Tabs, { TAB_HEIGHT } from './Components/Tabs'
import { getEstimatedItemSize, HomeCourseTab } from './constants'
import HomeCourseItem from './HomeCourseItem'
import QueryOrchestrator from './Query/QueryOrchestrator'

const HomeCourse = () => {
  const listRef = useRef<any>(null)
  const topInset = useTopInsets()
  const scrollY = useSharedValue(0)
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollY.value = event.contentOffset.y
    },
  })
  const paddingTop = useMemo(
    () => FULL_APP_HEADER_HEIGHT + topInset + TAB_HEIGHT + Theme.spacing.lr,
    [topInset],
  )

  const [activeTab, setActiveTab] = useState(HomeCourseTab.COURSE)

  const { data, refetch, isLoading } = QueryOrchestrator.useQueryData(activeTab)

  console.log('🚀 ~ HomeCourse ~ data:', data)
  const keyExtractor = (item: PreConstructedSection) => {
    return `${item.id}_${item.key}_${item.displayType}`
  }

  const renderItem = ({ item }: { item: PreConstructedSection }) => {
    try {
      return <HomeCourseItem data={item} type={activeTab} />
    } catch (error) {
      console.warn('Error rendering HomeCourseItem:', item.key, error)
      return null
    }
  }

  const scrollToTop = useCallback((animated = true) => {
    listRef.current?.scrollToOffset?.({
      animated,
    })
  }, [])

  useScrollToTop(scrollToTop)

  return (
    <Box>
      <StatusBar style="reverse" />
      <StickyHeader
        HiddenOnStickedComponent={<AppHeader logoColor />}
        ShownOnStickedComponent={<HomeCourseGradientBackground />}
        scrollY={scrollY}
        stickyTopOffset={topInset}>
        <Tabs activeTab={activeTab} onSelect={setActiveTab} />
      </StickyHeader>
      <HomeCourseBackgroundColor scrollY={scrollY} />

      <List
        ref={listRef}
        avoidingBottom
        useLegendList
        waitForInitialLayout
        recycleItems
        getEstimatedItemSize={getEstimatedItemSize}
        keyExtractor={keyExtractor}
        data={data || []}
        renderItem={renderItem}
        estimatedItemSize={80}
        contentContainerStyle={{ paddingTop }}
        onScroll={scrollHandler}
        style={styles.container}
        onRefresh={refetch}
        isLoading={isLoading}
        separator={null}
        avoidingBottomProps={{
          extendedValue: Theme.spacing.l,
        }}
      />
    </Box>
  )
}

export default HomeCourse

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
  },
})
