import { type FC, memo } from 'react'

import { HomeSectionItemType } from 'Repo/constants/home'
import type { PreConstructedSection } from 'Repo/entities/home-segment'
import CourseNewReleaseBanner from 'Features/PodCourse/Containers/CourseNewReleaseBanner'
import PodCourseBannerPromotion from 'Features/Purchase/Subscription/Banner/PodCourseBannerPromotion'

import { HomeCourseTab } from './constants'
import BookingMentorSections from './Sections/BookingMentorSections'
import ByCategoryCourses from './Sections/ByCategoryCourses'
import ContinueListening from './Sections/ContinueListening'
import CourseCategories from './Sections/CourseCategories'
import NewlyReleaseCourses from './Sections/NewlyReleaseCourses'
// Import section components
import TopTrendingCourses from './Sections/TopTrendingCourses'

interface HomeCourseItemProps {
  data: PreConstructedSection
  type?: HomeCourseTab
}

const HomeCourseItem: FC<HomeCourseItemProps> = ({ data, type }) => {
  const { key } = data

  if (type === HomeCourseTab.MENTOR) {
    // Render mentor sections directly if the type is MENTOR
    return <BookingMentorSections data={data} />
  }

  // Map section keys to specific components
  switch (key) {
    case HomeSectionItemType.TOP_TRENDING_COURSES:
      return <TopTrendingCourses data={data} />

    case HomeSectionItemType.NEW_RELEASE:
      return <NewlyReleaseCourses data={data} />

    case HomeSectionItemType.TOP_PICK_COURSES_IN_CATEGORY:
      return <ByCategoryCourses data={data} />

    case HomeSectionItemType.RECENTLY_LISTENED:
      return <ContinueListening data={data} />

    case HomeSectionItemType.COURSE_CATEGORY:
      return <CourseCategories data={data} />

    case HomeSectionItemType.COURSE_NEW_RELEASE_BANNER:
      return <CourseNewReleaseBanner />

    case HomeSectionItemType.POD_COURSE_BANNER_PROMOTION:
      return <PodCourseBannerPromotion />

    default:
      return null
  }
}

export default memo(HomeCourseItem)
