import type { PreConstructedSection } from 'Repo/entities/home-segment'
import QueryClient from 'Services/QueryClient'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import { getCurrentUserId } from 'Features/Auth/Stores'

import queryCourseSections from './queryCourseSections'
import queryMentorSections from './queryMentorSections'
import { HomeCourseTab } from '../constants'

const KEY = 'home_course_query'

const FUNC: any = {
  [HomeCourseTab.COURSE]: queryCourseSections,
  [HomeCourseTab.MENTOR]: queryMentorSections,
}

// Main orchestrator functions
const QueryOrchestrator = {
  clearCache: () => {
    QueryClient._invalidateQueries({ queryKey: [KEY, getCurrentUserId()] })
  },
  getQueryKey: (tab: HomeCourseTab) =>
    [KEY, getCurrentUserId(), tab].filter(Boolean),

  useQueryData: (tab: HomeCourseTab) => {
    const queryFn = FUNC[tab] || (() => [])

    return useRequestQuery<PreConstructedSection[]>(
      QueryOrchestrator.getQueryKey(tab),
      async () => {
        const response = await queryFn({})
        return response || []
      },
      {
        cacheForHours: 12,
        staleTime: 0,
      },
    )
  },
}

export default QueryOrchestrator
