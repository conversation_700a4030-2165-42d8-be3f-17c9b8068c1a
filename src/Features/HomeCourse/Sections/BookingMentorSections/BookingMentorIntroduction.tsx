import React from 'react'
import { StyleSheet } from 'react-native'

import Box from 'Components/Box'
import Card from 'Components/Card'
import Image from 'Components/Image'
import Text from 'Components/Text'
import { IMAGES } from 'Features/MentorBooking/constants'

const BookingMentorIntroduction = () => {
  return (
    <Card
      borderColor="rgba(210, 231, 255, 0.2)"
      backgroundColor="rgba(255, 255, 255, 0.05)"
      border
      style={styles.container}
      spacing="m"
      padding="m">
      <Box row center alignItemsCenter spacing="s">
        <Image source={IMAGES.STEP_AWAIT} style={styles.image} />
        <Text type="h2" light>
          Gọi 1-1 với Chuyên gia
        </Text>
      </Box>
      <Text type="large" themeColor="subText" themeSource="tabMode" center>
        Gọi video call với Chuyên gia để thảo luận các vấn đề mà bạn quan tâm
        nhất.
      </Text>
    </Card>
  )
}

export default BookingMentorIntroduction

const styles = StyleSheet.create({
  container: {},
  image: {
    width: 30,
    aspectRatio: 1,
  },
})
