import { sample } from 'lodash'
import { memo, useCallback } from 'react'
import { StyleSheet } from 'react-native'

import Box from 'Components/Box'
import Icon from 'Components/Icon'
import Image from 'Components/Image'
import ScaleTouchable from 'Components/ScaleTouchable'
import Text from 'Components/Text'
import type { BookingMentor } from 'Repo/entities/mentorBooking'
import {
  accessibilityWithText,
  disableAccessibilityIncludeChildren,
} from 'Services/Accessibility'
import { ExpertBookingSourceName } from 'Features/MentorBooking/constants'
import { goToMentorBookingDetail } from 'Features/MentorBooking/utils'

interface BookingMentorItemProps {
  mentor: BookingMentor
}

const BookingMentorItem = ({ mentor }: BookingMentorItemProps) => {
  const onPressMentor = useCallback(() => {
    goToMentorBookingDetail({
      mentorId: mentor.mentorId,
      mentorName: mentor.mentor.name,
      clickSource: ExpertBookingSourceName.HOME_MENTOR_SECTION,
    })
  }, [mentor])
  const avatarUrl = sample(mentor.mentor.avatars)

  const source = avatarUrl && { uri: avatarUrl }

  return (
    <ScaleTouchable
      onPress={onPressMentor}
      key={mentor.id}
      {...accessibilityWithText(mentor.mentor.name)}>
      <Box
        row
        alignItemsCenter
        spacing="m"
        {...disableAccessibilityIncludeChildren()}>
        <Box flex1>
          <Image source={source} style={styles.avatar} />
        </Box>
        <Box flex1 spacing="m">
          <Text type="h4" numberOfLines={1} themeSource="tabMode">
            {mentor.mentor.name}
          </Text>
          {mentor.mentor.headline && (
            <Text numberOfLines={2} themeSource="tabMode">
              {mentor.mentor.headline}
            </Text>
          )}
          <Box row alignItemsCenter spacing="s" style={styles.navigator}>
            <Text light>Xem chi tiết</Text>
            <Icon name="chevron_right" light />
          </Box>
        </Box>
      </Box>
    </ScaleTouchable>
  )
}

export default memo(BookingMentorItem)

const styles = StyleSheet.create({
  navigator: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  avatar: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 10,
  },
})
