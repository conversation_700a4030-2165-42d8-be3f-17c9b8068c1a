import { memo } from 'react'

import Box from 'Components/Box'
import type { BookingMentor } from 'Repo/entities/mentorBooking'
import SectionHeader from 'Features/HomePodcast/Components/SectionHeader'

import BookingMentorIntroduction from './BookingMentorIntroduction'
import BookingMentorItem from './BookingMentorItem'

interface BookingMentorSectionsProps {
  mentors: BookingMentor[]
  title?: string
  metadata?: any
}

const BookingMentorSections = ({
  mentors,
  title,
  metadata,
}: BookingMentorSectionsProps) => {
  if (!mentors?.length) return null

  return (
    <Box paddingHorizontal={'m'} spacing="xxl">
      <BookingMentorIntroduction />
      <Box spacing="m">
        <SectionHeader
          sectionTitle={`Chuyên gia hiện có (${mentors.length || 0})`}
          theme="dark"
          contentSpacing={null as any}
        />
        {mentors.map((mentor: BookingMentor) => {
          return <BookingMentorItem mentor={mentor} key={mentor.id} />
        })}
      </Box>
    </Box>
  )
}

export default memo(BookingMentorSections)
