import { useMemo } from 'react'

import {
  useBookingFeatureAvailable,
  useBookingMentors,
} from 'Features/MentorBooking/Stores/MentorBookingStore'

export const useBookingMentorSections = () => {
  const isAvailable = useBookingFeatureAvailable()
  const mentors = useBookingMentors()

  const data = useMemo(() => {
    if (!isAvailable || !mentors?.length) {
      return []
    }

    // Filter active mentors and limit to first 5
    return mentors.filter(mentor => mentor.status).slice(0, 5)
  }, [isAvailable, mentors])

  return {
    data,
    isLoading: false, // Data comes from store, so no loading state
    error: null,
    isAvailable,
  }
}
