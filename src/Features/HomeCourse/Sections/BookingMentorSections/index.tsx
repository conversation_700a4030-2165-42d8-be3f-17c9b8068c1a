import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import BookingMentorSections from './BookingMentorSections'
import { useBookingMentorSections } from './hooks/useBookingMentorSections'

interface BookingMentorSectionsContainerProps {
  data: PreConstructedSection
}

const BookingMentorSectionsContainer = ({
  data,
}: BookingMentorSectionsContainerProps) => {
  const {
    data: mentors,
    isLoading,
    error,
    isAvailable,
  } = useBookingMentorSections()

  if (!isAvailable || isLoading) {
    return null
  }

  if (error || !mentors?.length) {
    return null
  }

  return (
    <BookingMentorSections
      mentors={mentors}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(BookingMentorSectionsContainer)
