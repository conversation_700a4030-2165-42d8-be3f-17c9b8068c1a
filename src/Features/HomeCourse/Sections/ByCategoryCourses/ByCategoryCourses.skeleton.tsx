import Box from 'Components/Box'
import CourseCollectionSkeleton from 'Composites/Course/CourseCollection.skeleton'
import CourseHorizontalOneRowListSkeleton from 'Composites/Course/CourseHorizontalOneRowList.skeleton'
import CourseOneSectionSkeleton from 'Composites/Course/CourseOneSection.skeleton'
import CourseVerticalSectionListSkeleton from 'Composites/Course/CourseVerticalSectionList.skeleton'
import SectionHeaderSkeleton from 'Composites/SectionHeader/SectionHeader.skeleton'

export const ByCategoryCoursesSkeleton = ({
  total = 0,
}: {
  total?: number
}) => {
  let content
  if (total === 1) {
    content = <CourseOneSectionSkeleton />
  } else if (total === 2) {
    content = <CourseHorizontalOneRowListSkeleton />
  } else if (total <= 3) {
    content = <CourseVerticalSectionListSkeleton numRows={total} />
  } else {
    content = <CourseCollectionSkeleton />
  }

  return (
    <Box spacing="m" marginBottom="xxl">
      <SectionHeaderSkeleton />
      {content}
    </Box>
  )
}

export default ByCategoryCoursesSkeleton
