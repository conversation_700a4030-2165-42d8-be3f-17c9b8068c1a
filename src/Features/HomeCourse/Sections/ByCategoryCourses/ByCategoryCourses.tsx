import Box from 'Components/Box'
import { HomeSectionItemType } from 'Repo/constants/home'
import type { Course } from 'Repo/entities/course'
import NavigationService from 'Services/NavigationService'
import useCategoryHeadline from 'Features/HomeRecommendation/Hooks/useCategoryHeadline'
import Analytics from 'Trackings/Analytics'
import CourseCollection from 'Composites/Course/CourseCollection'
import CourseHorizontalOneRowList from 'Composites/Course/CourseHorizontalOneRowList'
import CourseOneSection from 'Composites/Course/CourseOneSection'
import CourseVerticalSectionList from 'Composites/Course/CourseVerticalSectionList'
import SectionHeader from 'Composites/SectionHeader'

interface ByCategoryCoursesProps {
  courses: Course[]
  title?: string
  metadata?: any
}

const ByCategoryCourses = ({
  courses,
  title,
  metadata,
}: ByCategoryCoursesProps) => {
  const categoryId = metadata?.payload?.categoryIds?.[0]
  const { name, icon } = useCategoryHeadline({ categoryId })

  const onPressTitle = () => {
    Analytics.viewVerticalListAllItemsForCategory(
      HomeSectionItemType.TOP_PICK_COURSES_IN_CATEGORY,
    )
    NavigationService.navigate('VerticalCourseRecommendListScreen', {
      params: {
        title,
        requestKey: 'TopPickCoursesInCategory',
        categoryName: name,
        categoryIcon: icon,
        getData: () => courses,
      },
    })
  }

  let content
  const total = courses?.length || 0
  if (total === 1) {
    content = <CourseOneSection data={courses[0]} />
  } else if (total === 2) {
    content = <CourseHorizontalOneRowList data={courses} />
  } else if (total <= 3) {
    content = (
      <CourseVerticalSectionList data={courses} numRows={courses?.length} />
    )
  } else {
    content = <CourseCollection data={courses} onViewMore={onPressTitle} />
  }

  return (
    <Box spacing="m" marginBottom="xxl">
      <SectionHeader
        categoryName={name}
        categoryIcon={icon}
        title={title as any}
        onPressTitle={courses?.length === 1 ? undefined : onPressTitle}
        theme="dark"
      />
      {content}
    </Box>
  )
}

export default ByCategoryCourses
