import { includes, partition, shuffle, take, uniq } from 'lodash'

import { CourseService } from 'Repo/applications/course'
import type { Course } from 'Repo/entities/course'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import PurchaseStoreService from 'Features/Purchase/Services/PurchaseStoreService'

// Rule Shuffle: Chưa mua xếp lên trướ<PERSON>, đã mua xếp ra sau. Shuffle list chưa mua.
// Avoid duplicates in adjacent rows: Tránh trùng lặp trong các hàng liền kề
const shuffleRule = (
  queryData: Course[] = [],
  others: Record<string, Course[]> = {},
) => {
  try {
    if (!queryData?.length) return queryData
    const purchased = PurchaseStoreService.getCoursesPurchased()
    const purchasedIds = purchased.map(item => item.id)
    const otherIds = Object.values(others)
      .map(arr => take(arr, 2).map(item => item.id))
      ?.flat?.()
    const allOtherIds = uniq([...otherIds, ...purchasedIds])
    const [nonMatching, matching] = partition(
      queryData,
      obj => !includes(allOtherIds, obj.id),
    )

    return [...shuffle(nonMatching), ...matching]
  } catch (_e) {
    return queryData
  }
}

export const useByCategoryCourses = (categoryId?: string) => {
  return useRequestQuery(
    ['useTopPickCoursesInCategoryId', categoryId],
    async () => {
      if (!categoryId) return []

      const res = await CourseService.getCoursesByCategoryId({
        categoryId,
      })

      // For now, we'll apply shuffleRule with empty others object
      // In the future, we could enhance this to get data from other sections
      return shuffleRule(res?.items || [], {})
    },
    {
      cacheForHours: 2,
      staleTime: 0,
    },
  )
}
