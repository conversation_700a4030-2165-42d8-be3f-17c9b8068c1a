import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import ByCategoryCourses from './ByCategoryCourses'
import ByCategoryCoursesSkeleton from './ByCategoryCourses.skeleton'
import { useByCategoryCourses } from './hooks/useByCategoryCourses'

interface ByCategoryCoursesContainerProps {
  data: PreConstructedSection
}

const ByCategoryCoursesContainer = ({
  data,
}: ByCategoryCoursesContainerProps) => {
  const categoryId = data.metadata?.payload?.categoryIds?.[0]
  const { data: courses, isLoading, error } = useByCategoryCourses(categoryId)
  console.log('🚀 ~ courses:', courses, data, isLoading)

  if (isLoading) {
    return (
      <ByCategoryCoursesSkeleton total={data.metadata?.payload?.total || 0} />
    )
  }

  if (error || !courses?.length) {
    return null
  }

  return (
    <ByCategoryCourses
      courses={courses}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(ByCategoryCoursesContainer)
