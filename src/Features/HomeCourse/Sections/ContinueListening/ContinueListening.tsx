import Box from 'Components/Box'
import type { Course } from 'Repo/entities/course'
import CourseVerticalSectionList from 'Composites/Course/CourseVerticalSectionList'
import SectionHeader from 'Composites/SectionHeader'

interface ContinueListeningProps {
  courses: Course[]
  title?: string
  metadata?: any
}

const ContinueListening = ({
  courses,
  title,
  metadata,
}: ContinueListeningProps) => {
  const displayTitle = title || 'Tiếp tục học'

  return (
    <Box spacing="m">
      <SectionHeader title={displayTitle} theme="dark" />
      <CourseVerticalSectionList
        data={courses}
        numRows={courses?.length || 3}
      />
    </Box>
  )
}

export default ContinueListening
