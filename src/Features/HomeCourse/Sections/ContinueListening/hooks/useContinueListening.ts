import { CourseService } from 'Repo/applications/course'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'

export const useContinueListening = () => {
  return useRequestQuery(
    ['continueListeningCourses'],
    async () => {
      // For now, we'll use a generic courses endpoint
      // In the future, we should create a specific endpoint for continue listening
      const res = await CourseService.getCourses({ limit: 10 })
      return res?.items || []
    },
    {
      cacheForHours: 0.5, // 30 minutes
      staleTime: 0,
    },
  )
}
