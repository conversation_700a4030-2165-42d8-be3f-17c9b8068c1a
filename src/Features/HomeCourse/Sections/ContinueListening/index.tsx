import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import ContinueListening from './ContinueListening'
import ContinueListeningSkeleton from './ContinueListening.skeleton'
import { useContinueListening } from './hooks/useContinueListening'

interface ContinueListeningContainerProps {
  data: PreConstructedSection
}

const ContinueListeningContainer = ({
  data,
}: ContinueListeningContainerProps) => {
  const { data: courses, isLoading, error } = useContinueListening()

  if (isLoading) {
    return <ContinueListeningSkeleton />
  }

  if (error || !courses?.length) {
    return null
  }

  return (
    <ContinueListening
      courses={courses}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(ContinueListeningContainer)
