import Box from 'Components/Box'
import { Theme } from 'Components/Theme'
import SectionHeaderSkeleton from 'Composites/SectionHeader/SectionHeader.skeleton'

export const CourseCategoriesSkeleton = () => {
  return (
    <Box spacing="m">
      <SectionHeaderSkeleton />
      <Box
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: Theme.spacing.s,
          paddingHorizontal: Theme.spacing.m,
        }}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Box
            key={index}
            style={{
              width: '30%',
              height: 80,
              backgroundColor: Theme.colors.skeleton,
              borderRadius: Theme.spacing.s,
            }}
          />
        ))}
      </Box>
    </Box>
  )
}

export default CourseCategoriesSkeleton
