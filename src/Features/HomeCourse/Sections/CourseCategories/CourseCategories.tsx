import { take } from 'lodash'
import { memo, useCallback, useMemo } from 'react'
import { StyleSheet } from 'react-native'

import Box from 'Components/Box'
import ScaleTouchable from 'Components/ScaleTouchable'
import SuspenseRenderer from 'Components/SuspenseRenderer'
import SvgIcon from 'Components/SvgIcon'
import Text from 'Components/Text'
import { Theme, ThemeSelectConsumer } from 'Components/Theme'
import { MediaType } from 'Repo/constants/media'
import type { Category } from 'Repo/entities/category'
import {
  accessibilityWithText,
  disableAccessibilityIncludeChildren,
} from 'Services/Accessibility'
import NavigationService from 'Services/NavigationService'
import SectionHeader from 'Features/HomePodcast/Components/SectionHeader'

interface CourseCategoriesProps {
  categories: Category[]
  title?: string
  metadata?: any
}

const CourseCategories = ({
  categories,
  title,
  metadata,
}: CourseCategoriesProps) => {
  const displayTitle = title || 'Danh mục'

  const onPressCategory = useCallback((category: Category) => {
    NavigationService.navigate('CategoryDetailScreen', {
      params: {
        categoryId: category.id,
        categoryName: category.name,
        mediaType: MediaType.COURSE,
      },
    })
  }, [])

  const items = useMemo(() => categories && take(categories, 6), [categories])

  if (!categories?.length) return null

  return (
    <Box marginTop="xl">
      <SectionHeader sectionTitle={displayTitle} theme="dark" />
      <SuspenseRenderer suspense loading={null}>
        <Box paddingHorizontal={'m'} paddingVertical={'s'}>
          {items?.map((item: Category) => {
            return (
              <ScaleTouchable
                onPress={() => onPressCategory(item)}
                key={item.id}
                {...accessibilityWithText(item.name)}>
                <Box
                  row
                  alignItemsCenter
                  style={styles.itemStyle}
                  marginBottom={'m'}
                  {...disableAccessibilityIncludeChildren()}>
                  <ThemeSelectConsumer
                    storeKey="tabMode"
                    value={{
                      light: '#DBEAFA',
                      dark: 'rgba(219, 234, 250, 0.3)',
                    }}>
                    {backgroundColor => (
                      <Box
                        style={[styles.iconBackground, { backgroundColor }]}
                        alignItemsCenter
                        center
                        marginRight="m">
                        {!!item.icon && (
                          <SvgIcon icon={item.icon} width={24} height={24} />
                        )}
                      </Box>
                    )}
                  </ThemeSelectConsumer>
                  <Text type="large" numberOfLines={1} themeSource="tabMode">
                    {item.name}
                  </Text>
                </Box>
              </ScaleTouchable>
            )
          })}
        </Box>
      </SuspenseRenderer>
    </Box>
  )
}

const styles = StyleSheet.create({
  iconBackground: {
    width: 42,
    height: 42,
    borderRadius: 42 / 2,
    backgroundColor: Theme.colors.lightDark,
  },
  itemStyle: {
    height: 42,
  },
})

export default memo(CourseCategories)
