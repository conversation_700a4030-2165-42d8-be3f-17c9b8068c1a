import { CategoryService } from 'Repo/applications/category'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'

export const useCourseCategories = ({
  categoryIds = [],
}: {
  categoryIds?: number[]
}) => {
  return useRequestQuery(
    ['courseCategories'],
    async () => {
      if (!categoryIds?.length) return []

      let res = await CategoryService.getCategoriesByIds(categoryIds)
      if (!res?.length || res?.length < 3) return []

      return res
    },
    {
      suspense: true,
      cacheForHours: 24,
    },
  )
}
