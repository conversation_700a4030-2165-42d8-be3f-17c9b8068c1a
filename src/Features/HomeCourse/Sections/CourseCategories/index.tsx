import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import CourseCategories from './CourseCategories'
import CourseCategoriesSkeleton from './CourseCategories.skeleton'
import { useCourseCategories } from './hooks/useCourseCategories'

interface CourseCategoriesContainerProps {
  data: PreConstructedSection
}

const CourseCategoriesContainer = ({
  data,
}: CourseCategoriesContainerProps) => {
  const categoryIds = data.metadata?.payload?.categoryIds
  const {
    data: categories,
    isLoading,
    error,
  } = useCourseCategories(categoryIds)

  if (isLoading) {
    return <CourseCategoriesSkeleton />
  }

  if (error || !categories?.length) {
    return null
  }

  return (
    <CourseCategories
      categories={categories}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(CourseCategoriesContainer)
