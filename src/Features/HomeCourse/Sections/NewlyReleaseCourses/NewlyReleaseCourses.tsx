import { useIntl } from 'react-intl'

import Box from 'Components/Box'
import { HomeSectionItemType } from 'Repo/constants/home'
import type { Course } from 'Repo/entities/course'
import NavigationService from 'Services/NavigationService'
import Analytics from 'Trackings/Analytics'
import CourseHorizontalOneRowList from 'Composites/Course/CourseHorizontalOneRowList'
import SectionHeader from 'Composites/SectionHeader'

// Import messages - we'll need to check if this exists
// import messages from '../../messages'

interface NewlyReleaseCoursesProps {
  courses: Course[]
  title?: string
  metadata?: any
}

const NewlyReleaseCourses = ({
  courses,
  title,
  metadata,
}: NewlyReleaseCoursesProps) => {
  const _intl = useIntl()

  // Fallback title if messages import doesn't work
  const displayTitle = title || 'Khóa học mới phát hành'

  const onPressTitle = () => {
    Analytics.viewVerticalListAllItemsForCategory(
      HomeSectionItemType.NEWLY_RELEASE_COURSES,
    )
    NavigationService.navigate('VerticalCourseRecommendListScreen', {
      params: {
        title: displayTitle,
        page: 'newly_release',
      },
    })
  }

  return (
    <Box spacing="m" marginBottom="xxl">
      <SectionHeader
        title={displayTitle}
        onPressTitle={onPressTitle}
        theme="dark"
      />
      <CourseHorizontalOneRowList data={courses} onViewMore={onPressTitle} />
    </Box>
  )
}

export default NewlyReleaseCourses
