import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import { useNewlyReleaseCourses } from './hooks/useNewlyReleaseCourses'
import NewlyReleaseCourses from './NewlyReleaseCourses'
import NewlyReleaseCoursesSkeleton from './NewlyReleaseCourses.skeleton'

interface NewlyReleaseCoursesContainerProps {
  data: PreConstructedSection
}

const NewlyReleaseCoursesContainer = ({
  data,
}: NewlyReleaseCoursesContainerProps) => {
  const { data: courses, isLoading, error } = useNewlyReleaseCourses()

  if (isLoading) {
    return <NewlyReleaseCoursesSkeleton />
  }

  if (error || !courses?.length) {
    return null
  }

  return (
    <NewlyReleaseCourses
      courses={courses}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(NewlyReleaseCoursesContainer)
