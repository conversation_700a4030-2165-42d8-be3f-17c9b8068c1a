import Box from 'Components/Box'
import { MediaType } from 'Repo/constants/media'
import type { Course } from 'Repo/entities/course'
import NavigationService from 'Services/NavigationService'
import RankingCourseHorizontalOneRowList from 'Composites/Course/RankingCourseHorizontalOneRowList'
import SectionHeader from 'Composites/SectionHeader'

interface TopTrendingCoursesProps {
  courses: Course[]
  title?: string
  metadata?: any
}

const TopTrendingCourses = ({
  courses,
  title,
  metadata,
}: TopTrendingCoursesProps) => {
  const onPressTitle = () => {
    NavigationService.navigate('TrendingScreen', {
      params: { mediaType: MediaType.COURSE },
    })
  }

  return (
    <Box spacing="m" marginBottom="xxl">
      {title && (
        <SectionHeader title={title} onPressTitle={onPressTitle} theme="dark" />
      )}
      <RankingCourseHorizontalOneRowList
        data={courses}
        onViewMore={onPressTitle}
      />
    </Box>
  )
}

export default TopTrendingCourses
