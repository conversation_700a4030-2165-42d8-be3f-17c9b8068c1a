import { first } from 'lodash'

import { CourseService } from 'Repo/applications/course'
import useRequestQuery from 'Services/QueryClient/useRequestQuery'
import { HomeCourseTab } from 'Features/HomeCourse/constants'
import { updateHomeCourseColor } from 'Features/HomeCourse/Store/HomeCourseStore'
import { takeCourseCoverImage } from 'Features/PodCourse/Utils/courseImageParser'

export const useTopTrendingCourses = () => {
  return useRequestQuery(
    ['topTrendingCourses'],
    async () => {
      const res = await CourseService.getTrendingCourses({ limit: 10 })
      const firstItem = first(res)
      if (firstItem)
        updateHomeCourseColor(
          HomeCourseTab.COURSE,
          takeCourseCoverImage(firstItem)?.coverColor,
        )
      return res || []
    },
    {
      cacheForHours: 2,
      staleTime: 0,
    },
  )
}
