import { memo } from 'react'

import type { PreConstructedSection } from 'Repo/entities/home-segment'

import { useTopTrendingCourses } from './hooks/useTopTrendingCourses'
import TopTrendingCourses from './TopTrendingCourses'
import TopTrendingCoursesSkeleton from './TopTrendingCourses.skeleton'

interface TopTrendingCoursesContainerProps {
  data: PreConstructedSection
}

const TopTrendingCoursesContainer = ({
  data,
}: TopTrendingCoursesContainerProps) => {
  const { data: courses, isLoading, error } = useTopTrendingCourses()

  if (isLoading) {
    return <TopTrendingCoursesSkeleton />
  }

  if (error || !courses?.length) {
    return null
  }

  return (
    <TopTrendingCourses
      courses={courses}
      title={data.title}
      metadata={data.metadata}
    />
  )
}

export default memo(TopTrendingCoursesContainer)
