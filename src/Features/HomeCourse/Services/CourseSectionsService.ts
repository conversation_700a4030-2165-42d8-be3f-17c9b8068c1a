import { pick } from 'lodash'

import { HomeService } from 'Repo/applications/home'
import { HomeSectionItemType } from 'Repo/constants/home'
import {
  DisplayType,
  type PreConstructedSection,
} from 'Repo/entities/home-segment'
import { hasUserAuthenticated } from 'Features/Auth/Stores'
import {
  getCourseHomePageConfigCache,
  setCourseHomePageConfigCache,
} from 'Features/Home/Stores'
import type { HomePageConfig } from 'Features/Home/Stores/HomePageConfigStore'
import { getHomePageLayout } from 'Features/Home/util'
import { getOnboardingCategoryIds } from 'Features/OnBoarding/Stores'
import { getDateAtNext10PM, isExpired } from 'Utils/datetime'

class CourseSectionsService {
  constructor() {}

  async getSections(): Promise<PreConstructedSection[]> {
    try {
      let result: any[] = []

      if (!hasUserAuthenticated()) {
        // Guest user flow
        const categoryGroupIds = await getOnboardingCategoryIds()
        const listConfigFromServer =
          await HomeService.getCourseHomePage(categoryGroupIds)
        result = getHomePageLayout(listConfigFromServer?.data)
      } else {
        // Authenticated user flow with cache
        const currentCacheData = getCourseHomePageConfigCache()

        if (
          currentCacheData &&
          currentCacheData?.expireTime &&
          !isExpired(currentCacheData.expireTime)
        ) {
          // Use cached data
          result = currentCacheData.homePageConfig
        } else {
          // Fetch fresh data and cache it
          const categoryGroupIds = await getOnboardingCategoryIds()
          const listConfigFromServer =
            await HomeService.getCourseHomePage(categoryGroupIds)
          const dataReturn = getHomePageLayout(listConfigFromServer?.data)

          const cacheData = {
            homePageConfig: dataReturn,
            expireTime: getDateAtNext10PM(),
          } as HomePageConfig

          setCourseHomePageConfigCache(cacheData)
          result = dataReturn
        }
      }
      console.log('🚀 ~ CourseSectionsService ~ getSections ~ result:', result)

      // Convert to PreConstructedSection format
      const sections: PreConstructedSection[] =
        result?.map((c, index) => ({
          id: index + 1,
          key: c.key,
          title: c.title || this.getSectionTitle(c.key),
          position: index,
          displayType: this.getDefaultDisplayType(c.key),
          contentType: 'course' as const,
          isPopulated: true,
          items: c.payload || [],
          metadata: pick(c, ['title', 'payload']),
        })) || []

      // manual add banner sections at top and bottom sections
      // CourseNewReleaseBanner and PodCourseBannerPromotion
      return [
        {
          id: -1,
          key: HomeSectionItemType.POD_COURSE_BANNER_PROMOTION,
          title: 'Khóa học nổi bật',
          position: sections.length + 1,
          displayType: DisplayType.BANNER,
          contentType: 'course',
          isPopulated: true,
          items: [],
          metadata: {},
        },

        ...sections,
        {
          id: sections.length + 1,
          key: HomeSectionItemType.COURSE_NEW_RELEASE_BANNER,
          title: 'Khóa học mới',
          position: -1,
          displayType: DisplayType.BANNER,
          contentType: 'course',
          isPopulated: true,
          items: [],
          metadata: {},
        },
      ]
    } catch (error) {
      console.warn('Failed to load course sections, using fallback', error)
      return this.generateFallbackSections()
    }
  }

  private async generateFallbackSections(): Promise<PreConstructedSection[]> {
    // Generate basic fallback sections
    const sectionKeys = [
      HomeSectionItemType.FEATURED_CONTENT,
      HomeSectionItemType.TOP_TRENDING_COURSES,
      HomeSectionItemType.COURSE_CATEGORY,
      HomeSectionItemType.RECENTLY_LISTENED,
    ]

    const sections: PreConstructedSection[] = sectionKeys.map((key, index) => ({
      id: index + 1,
      key,
      title: this.getSectionTitle(key),
      position: index,
      displayType: this.getDefaultDisplayType(key),
      contentType: 'course' as const,
      isPopulated: false,
      items: [],
      metadata: {},
    }))

    return sections
  }

  private getDefaultDisplayType(key: HomeSectionItemType): DisplayType {
    switch (key) {
      case HomeSectionItemType.FEATURED_CONTENT:
        return DisplayType.BANNER

      case HomeSectionItemType.COURSE_CATEGORY:
        return DisplayType.GRID

      case HomeSectionItemType.TOP_TRENDING_COURSES:
      case HomeSectionItemType.TOP_PICK_COURSES_IN_CATEGORY:
        return DisplayType.HORIZONTAL

      case HomeSectionItemType.RECENTLY_LISTENED:
        return DisplayType.TWO_ROWS

      default:
        return DisplayType.TWO_ROWS
    }
  }

  private getSectionTitle(key: HomeSectionItemType): string {
    switch (key) {
      case HomeSectionItemType.FEATURED_CONTENT:
        return 'Nổi bật'
      case HomeSectionItemType.TOP_TRENDING_COURSES:
        return 'Khóa học thịnh hành'
      case HomeSectionItemType.TOP_PICK_COURSES_IN_CATEGORY:
        return 'Đề xuất cho bạn'
      case HomeSectionItemType.COURSE_CATEGORY:
        return 'Danh mục'
      case HomeSectionItemType.RECENTLY_LISTENED:
        return 'Tiếp tục học'
      case HomeSectionItemType.SUGGESTED_COURSES:
        return 'Gợi ý khóa học'
      default:
        return 'Khóa học'
    }
  }
}

export default new CourseSectionsService()
