import { HomeSectionItemType } from 'Repo/constants/home'
import {
  DisplayType,
  type PreConstructedSection,
} from 'Repo/entities/home-segment'
import BookingService from 'Features/MentorBooking/Services/BookingService'
import MentorBookingStore from 'Features/MentorBooking/Stores/MentorBookingStore'

class MentorSectionsService {
  constructor() {}

  async getSections(): Promise<PreConstructedSection[]> {
    await BookingService.waitForCompleted()
    const mentorBookingState = MentorBookingStore.get()

    // Check if mentor booking feature is available and has data
    if (
      !mentorBookingState.available ||
      !Object.keys(mentorBookingState.data).length
    ) {
      return []
    }

    // Convert booking mentors to sections
    const mentors = Object.values(mentorBookingState.data).filter(Boolean)

    if (!mentors.length) {
      return []
    }

    const sections: PreConstructedSection[] = [
      {
        id: 1,
        key: HomeSectionItemType.EXPERT_BOOKING,
        title: '<PERSON><PERSON> nổ<PERSON> bật',
        position: 0,
        displayType: DisplayType.TWO_ROWS,
        contentType: 'mentor' as any,
        isPopulated: true,
        items: mentors.map(mentor => ({
          id: mentor.id,
          mentorId: mentor.mentorId,
          title: mentor.mentor.name,
          subtitle: mentor.mentor.headline,
          imageUrl: mentor.mentor.avatars?.[0],
          price: mentor.price,
          sessionLength: mentor.sessionLength,
          sessionUnit: mentor.sessionUnit,
          sessionTitle: mentor.sessionTitle,
          about: mentor.about,
          featuredImages: mentor.featuredImages,
        })) as any[],
        metadata: {
          totalItems: mentors.length,
        },
      },
    ]

    return sections
  }
}

export default new MentorSectionsService()
