import Store from 'Services/Store'

import { HomeCourseTab } from '../constants'

interface HomeCourseStoreState {
  [HomeCourseTab.COURSE]: { color: any }
  [HomeCourseTab.MENTOR]: { color: any }
}

const HomeCourseStore = new Store<HomeCourseStoreState>(() => ({
  [HomeCourseTab.COURSE]: { color: undefined },
  [HomeCourseTab.MENTOR]: { color: undefined },
}))

export default HomeCourseStore

export const useHomeCourseColor = (tab: HomeCourseTab) =>
  HomeCourseStore.shallow(state => state[tab]?.color)

export const updateHomeCourseColor = (tab: HomeCourseTab, color: any) => {
  HomeCourseStore.set(state => ({
    ...state,
    [tab]: {
      ...state[tab],
      color,
    },
  }))
}
