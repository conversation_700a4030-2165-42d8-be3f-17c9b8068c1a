export const IMAGES = {
  INST_STEP_CONTACT: require('./Images/inst_step_contact.webp'),
  INST_STEP_AWAIT: require('./Images/inst_step_await.webp'),
  INST_STEP_BOOKED: require('./Images/inst_step_booked.webp'),
  STEP_CONTACT: require('./Images/step_contact.webp'),
  STEP_AWAIT: require('./Images/step_await.webp'),
  STEP_BOOKED: require('./Images/step_booked.webp'),
  STEP_DONE: require('./Images/step_done.webp'),
  STEP_AWAIT_PENDING: require('./Images/step_await_pending.webp'),
  STEP_BOOKED_PENDING: require('./Images/step_booked_pending.webp'),
  EB_SECTION_BG: require('./Images/eb_section_bg.webp'),
  BETA_LABEL: require('./Images/beta_label.webp'),
  IC_HIDDEN: require('./Images/ic_hidden.webp'),
  BTN_CALL_1_1: require('./Images/bg_btn_book_mentor.webp'),
  IC_CALL: require('./Images/ic_call.webp'),
  IC_ZALO: require('./Images/ic_zalo.webp'),
  IMG_CONTACT_CS: require('./Images/img_contact.webp'),
}

export enum ExpertBookingSourceName {
  HOMEPAGE_BANNER = 'homepage_banner',
  PODCOURSE_TAB_BANNER = 'podcourse_tab_banner',
  IN_APP_POPUP = 'in_app_popup',
  AUTHOR_PAGE = 'author_page',
  EXPERT_LISTING_PAGE = 'expert_listing_page',
  HOME_MENTOR_SECTION = 'home_mentor_section',
}
