import { memo } from 'react'
import { useIntl } from 'react-intl'
import { ImageBackground, StyleSheet } from 'react-native'

import Box from 'Components/Box'
import GradientView from 'Components/GradientView'
import Text from 'Components/Text'
import { Theme } from 'Components/Theme'

import messages from './messages'
import NotifButton from './NotifButton'
import SuggestButton from './SuggestButton'
import { IMAGES } from '../../constants'

const CourseNewReleaseBanner = () => {
  const intl = useIntl()

  return (
    <ImageBackground
      source={IMAGES.COURSE_NEW_RELEASE_BANNER}
      style={[styles.img]}
      imageStyle={styles.image}>
      <GradientView
        style={styles.gradientView}
        colors={['#513F63', '#513F6300']}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 1 }}>
        <Text light>{intl.formatMessage(messages.youWantMore)}</Text>
      </GradientView>
      <Box marginTop={'m'} flex1 marginLeft="m">
        <Box marginBottom="m">
          <Text style={styles.txt} bold light>
            {intl.formatMessage(messages.waitForNewRelease)}
          </Text>
          <Text style={styles.txt} bold light marginTop="s" color="#EF8F6D">
            {intl.formatMessage(messages.releaseEveryWeek)}
          </Text>
        </Box>
        <Box row alignItemsCenter>
          <NotifButton />
          <SuggestButton />
        </Box>
      </Box>
    </ImageBackground>
  )
}

export default memo(CourseNewReleaseBanner)

const styles = StyleSheet.create({
  img: {
    width: '100%',
    aspectRatio: 375 / 285,
    borderRadius: 20,
    marginTop: Theme.spacing.s,
  },
  image: {
    borderRadius: 20,
  },
  txt: {
    fontSize: 17,
  },
  gradientView: {
    height: 28,
    width: 280,
    paddingLeft: Theme.spacing.sm,
    marginTop: Theme.spacing.l,
    justifyContent: 'center',
    borderRadius: 14,
    marginLeft: 6,
  },
})
