import { debounce } from 'lodash'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import { useIntl } from 'react-intl'
import { StyleSheet } from 'react-native'
import PagerView from 'react-native-pager-view'

import Box, { type BoxProps } from 'Components/Box'
import Icon from 'Components/Icon'
import Image from 'Components/Image'
import ScaleTouchable from 'Components/ScaleTouchable'
import Text from 'Components/Text'
import { DarkTheme, Theme } from 'Components/Theme'
import {
  useCurrentSubscription,
  useHasSubscription,
} from 'Features/Auth/Stores'
import { goToSale } from 'Features/Purchase/utils'
import Dimensions from 'Constants/dimensions'

import DotIndicator from './DotIndicator'
import UpgradeBanner from './UpgradeBanner'
import {
  IMAGES,
  SaleSubscriptionSourceName,
  SubscriptionType,
} from '../../constants'
import { BANNER_UPGRADABLE_PLANS } from '../constants'
import messages from '../messages'

const PodCourseBannerPromotion = (boxProps: BoxProps) => {
  const listRef = useRef<any>(null)
  const [activePage, setActivePage] = useState(0)
  const intl = useIntl()
  const hasSubscription = useHasSubscription()
  const currentSubscription = useCurrentSubscription()
  const { isWebSubscription, isCodeSubscription, productId } =
    currentSubscription || {}
  const showBannerUpgrade =
    BANNER_UPGRADABLE_PLANS.includes(productId as any) &&
    !isWebSubscription &&
    !isCodeSubscription

  const onGoToSale = useCallback(
    () =>
      goToSale({
        sourceName: SaleSubscriptionSourceName.PODCOURSE_BANNER,
        showBannerMembership: true,
        subType: SubscriptionType.PRO,
        theme: 'dark',
      }),
    [],
  )

  useEffect(() => {
    const interval = setInterval(() => {
      if (listRef?.current) {
        if (activePage === 0) {
          listRef.current.setPage(1)
        } else {
          listRef.current.setPage(0)
        }
      }
    }, 5000)

    return () => {
      clearInterval(interval)
    }
  }, [activePage])

  const onPageChange = useCallback((e: any) => {
    const { position } = e.nativeEvent
    return debounce(() => {
      setActivePage(position)
    }, 100)()
  }, [])

  if (hasSubscription && !showBannerUpgrade) return null

  if (showBannerUpgrade) {
    return <UpgradeBanner {...boxProps} />
  }

  return (
    <Box
      marginHorizontal="m"
      marginBottom="xxl"
      style={styles.container}
      {...boxProps}>
      <Image source={IMAGES.PROMO_SUBS_BANNER} style={styles.bgImage} />
      <PagerView
        ref={listRef}
        style={styles.sliderContainer}
        onPageSelected={onPageChange}>
        <ScaleTouchable onPress={onGoToSale}>
          <Box row alignItemsCenter paddingBottom="s">
            <Box flex1>
              <Text light type="small">
                Giới thiệu{' '}
                <Text bold color="#FFC34B">
                  PodCourse
                </Text>
              </Text>
              <Text marginRight="s" light marginTop="s">
                Học từ những người giỏi nhất, trở thành phiên bản tốt nhất với
                định dạng video ngắn.
              </Text>
            </Box>
            <Icon
              name="chevron_right"
              color={Theme.colors.lightOpacity(0.6)}
              size={20}
            />
          </Box>
        </ScaleTouchable>
        <ScaleTouchable onPress={onGoToSale}>
          <Box row alignItemsCenter paddingBottom="s">
            <Box flex1>
              <Text light type="small">
                {intl.formatMessage(messages.subsBannerTitle2)}
              </Text>
              <Text marginRight="s" light marginTop="s">
                {intl.formatMessage(messages.subsBannerDesc2, {
                  link: (chunks: any) => (
                    <Text bold color="#FFC34B">
                      {chunks}
                    </Text>
                  ),
                })}
              </Text>
            </Box>
            <Icon
              name="chevron_right"
              color={Theme.colors.lightOpacity(0.6)}
              size={20}
            />
          </Box>
        </ScaleTouchable>
      </PagerView>
      <DotIndicator style={styles.dotContainer} activePage={activePage} />
    </Box>
  )
}

export default memo(PodCourseBannerPromotion)

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.m,
    borderRadius: 10,
    overflow: 'hidden',
    zIndex: 99,
    borderWidth: 1,
    borderColor: DarkTheme.colors.breakLine,
    marginTop: -10,
  },
  bgImage: {
    ...StyleSheet.absoluteFillObject,
  },
  sliderContainer: {
    width: Dimensions.width - 64,
    aspectRatio: 343 / 86,
  },
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 14,
    left: 16,
  },
})
