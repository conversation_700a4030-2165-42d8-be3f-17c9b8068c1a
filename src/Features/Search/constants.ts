import { MediaType } from 'Repo/constants/media'
import NavigationService from 'Services/NavigationService'
import { goToHomePodcast } from 'Features/HomePodcast/utils'

import messages from './messages'

export const MEDIA_TYPE_SEARCH = {
  BOOKS: 'books',
  ENGLISH_BOOKS: 'english-books',
  BOOK_SUMMARIES: 'book-summaries',
  EBOOKS: 'ebooks',
  PODCAST_CHANNELS: 'channels',
  PODCAST_EPISODES: 'episodes',
  COURSES: 'courses',
  AUTHORS: 'authors',
}

export const MEDIA_TYPE_SEARCH_MAP = {
  [MEDIA_TYPE_SEARCH.BOOKS]: MediaType.BOOK,
  [MEDIA_TYPE_SEARCH.ENGLISH_BOOKS]: MediaType.ENG_BOOK,
  [MEDIA_TYPE_SEARCH.BOOK_SUMMARIES]: MediaType.BOOK_SUMMARY,
  [MEDIA_TYPE_SEARCH.EBOOKS]: MediaType.EBOOK,
  [MEDIA_TYPE_SEARCH.PODCAST_CHANNELS]: MediaType.PODCAST_CHANNEL,
  [MEDIA_TYPE_SEARCH.PODCAST_EPISODES]: MediaType.PODCAST_EPISODE,
  [MEDIA_TYPE_SEARCH.COURSES]: MediaType.COURSE,
}

export const MEDIA_TYPE_SEARCH_MAP_TITLE = {
  [MEDIA_TYPE_SEARCH.BOOKS]: messages.viAudioBook,
  [MEDIA_TYPE_SEARCH.ENGLISH_BOOKS]: messages.enAudioBook,
  [MEDIA_TYPE_SEARCH.BOOK_SUMMARIES]: messages.bookSummary,
  [MEDIA_TYPE_SEARCH.EBOOKS]: messages.ebook,
  [MEDIA_TYPE_SEARCH.PODCAST_CHANNELS]: messages.podcastChannel,
  [MEDIA_TYPE_SEARCH.PODCAST_EPISODES]: messages.podcastEpisode,
  [MEDIA_TYPE_SEARCH.AUTHORS]: messages.author,
  [MEDIA_TYPE_SEARCH.COURSES]: messages.podCourse,
}

export const IMAGES = {
  NO_RESULT: require('./Images/img_search_no_result.webp'),
  give_and_set_cover: require('./Images/give_and_set_cover.webp'),
  audiobook_cover: require('./Images/audiobook_cover.webp'),
  podcourse_cover: require('./Images/podcourse_cover.webp'),
  engbook_cover: require('./Images/engbook_cover.webp'),
  ic_ebook: require('./Images/ic_ebook.webp'),
  ic_booksummary: require('./Images/ic_booksummary.webp'),
  ic_zen: require('./Images/ic_zen.webp'),
  ic_sleep: require('./Images/ic_sleep.webp'),
  ic_music: require('./Images/ic_music.webp'),
  ic_kids: require('./Images/ic_kids.webp'),
  ic_podcast: require('./Images/ic_podcast.webp'),
  ic_top_chart: require('./Images/ic_top_chart.webp'),
  ic_top_reviewer: require('./Images/ic_top_reviewer.webp'),
}

export const MAIN_CATEGORIES: any[] = [
  {
    id: 'audiobook',
    onPress: () => {
      NavigationService.navigate('AudioBookScreen')
    },
    icon: IMAGES.audiobook_cover,
    title: messages.viAudioBook,
    colors: ['#2A4761', '#4787BF'],
    aspectRatio: 120 / 104,
  },
  {
    id: 'podcourse',
    onPress: () => NavigationService.navigate('PodCourse'),
    icon: IMAGES.podcourse_cover,
    title: messages.podCourse,
    colors: ['#2B275B', '#6647BF'],
    aspectRatio: 120 / 117,
  },
  {
    id: 'engBook',
    onPress: () => NavigationService.navigate('EngBook'),
    icon: IMAGES.engbook_cover,
    title: messages.enAudioBook,
    colors: ['#612A2A', '#BF4747'],
    aspectRatio: 120 / 108,
  },
]

export const SUB_CATEGORIES: any[] = [
  {
    id: 'ebook',
    onPress: () => {
      NavigationService.navigate('EbookScreen')
    },
    icon: IMAGES.ic_ebook,
    title: messages.ebook,
    color: { light: '#F0E9C5', dark: '#393212' },
  },
  {
    id: 'bookSummary',
    onPress: () => {
      NavigationService.navigate('BookSummaryScreen')
    },
    icon: IMAGES.ic_booksummary,
    title: messages.summary,
    color: { light: '#DFF4FF', dark: '#193241' },
  },
  {
    id: 'meditation',
    onPress: () => {
      NavigationService.navigateNestedScreens(['Zen', 'Meditations'])
    },
    icon: IMAGES.ic_zen,
    title: messages.meditation,
    color: { light: '#D9EFD9', dark: '#223122' },
  },

  {
    id: 'sleepStory',
    onPress: () => {
      NavigationService.navigateNestedScreens(['Zen', 'SleepStories'])
    },
    icon: IMAGES.ic_sleep,
    title: messages.sleepStory,
    color: { light: '#DFE8FA', dark: '#25324C' },
  },
  {
    id: 'music',
    onPress: () => {
      NavigationService.navigateNestedScreens(['Zen', 'Music'])
    },
    icon: IMAGES.ic_music,
    title: messages.musicCategory,
    color: { light: '#F2E1F5', dark: '#3A1F3E' },
  },
  {
    id: 'kidsStories',
    onPress: () => {
      NavigationService.navigateNestedScreens(['Zen', 'KidsStories'])
    },
    icon: IMAGES.ic_kids,
    title: messages.kids,
    color: { light: '#FAEAEC', dark: '#432427' },
  },
  {
    id: 'podcast',
    onPress: goToHomePodcast,
    icon: IMAGES.ic_podcast,
    title: messages.podcast,
    color: { light: '#F5E5DE', dark: '#4C3329' },
  },
]
