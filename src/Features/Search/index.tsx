import { StyleSheet } from 'react-native'
import { memo, useCallback, useMemo } from 'react'
import { useIntl } from 'react-intl'

import TextInput from 'Components/TextInput'
import { responsive } from 'Utils/responsive'
import Box from 'Components/Box'
import { Theme, useThemeColor, useThemeSelect } from 'Components/Theme'
import AppHeader from 'Features/AppHeader'
import BLoC from 'Features/BLoC'
import VerticalListBackground from 'Components/VerticalListBackground'
import BackgroundScrollView from 'Components/BackgroundScrollView'

import messages from './messages'
import CategoriesRecommend from './Components/CategoriesRecommend'
import { goToSearchInput } from './utils'

const Search = () => {
  const intl = useIntl()

  const navigateToSearchPage = useCallback(() => {
    goToSearchInput()
  }, [])

  const bgInput = useThemeSelect({
    light: Theme.colors.light,
    dark: Theme.colors.white.white10,
  })

  const searchBoxStyle = useMemo(
    () => [styles.searchBox, { backgroundColor: bgInput }],
    [bgInput],
  )

  const iconColor = useThemeColor('mainText')
  const placeholderTextColor = useThemeColor('subText')

  return (
    <BackgroundScrollView
      ExternalFixedHeaderComponent={
        <Box>
          <AppHeader title={intl.formatMessage(messages.searchPageTitle)} />
          <BLoC.Accessible
            row
            alignItemsCenter
            marginBottom="m"
            paddingHorizontal="m"
            accessibilityLabel={intl.formatMessage(messages.searchTile)}>
            <TextInput
              touchOnly
              autoFocus
              icon="search_v2"
              placeholder={intl.formatMessage(messages.searchTile)}
              layoutStyle={searchBoxStyle}
              style={styles.container}
              textInputStyle={styles.textInput}
              placeholderTextColor={placeholderTextColor}
              iconColor={iconColor}
              autoCapitalize="none"
              clearable
              light
              onPressInput={navigateToSearchPage}
            />
          </BLoC.Accessible>
        </Box>
      }
      BackgroundComponent={VerticalListBackground}
      avoidingKeyboard>
      <CategoriesRecommend />
    </BackgroundScrollView>
  )
}

export default memo(Search)

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchBox: {
    borderRadius: responsive(10),
    height: 44,
  },
  textInput: {
    fontSize: 14,
  },
})
