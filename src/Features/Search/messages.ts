import { defineMessages } from 'react-intl'

import { MediaType } from 'Repo/constants/media'

const scope = 'search-screen'

export default defineMessages({
  searchTile: {
    id: `${scope}.searchTile`,
    defaultMessage: 'Search for content',
  },
  cancel: {
    id: `${scope}.cancel`,
    defaultMessage: 'Cancel',
  },
  enAudioBook: {
    id: `${scope}.enAudioBook`,
    defaultMessage: 'English audio books',
  },
  viAudioBook: {
    id: `${scope}.viAudioBook`,
    defaultMessage: 'Vietnamese audio books',
  },
  ebook: {
    id: `${scope}.ebook`,
    defaultMessage: 'Ebooks',
  },
  meditation: {
    id: `${scope}.meditation`,
    defaultMessage: 'Meditation',
  },
  sleepStory: {
    id: `${scope}.sleepStory`,
    defaultMessage: 'Sleep story',
  },
  music: {
    id: `${scope}.music`,
    defaultMessage: 'Music',
  },
  bookSummary: {
    id: `${scope}.bookSummary`,
    defaultMessage: 'Book summaries',
  },
  topResult: {
    id: `${scope}.topResult`,
    defaultMessage: 'Top Results',
  },
  books: {
    id: `${scope}.books`,
    defaultMessage: 'Audio Books',
  },
  bookSummaries: {
    id: `${scope}.bookSummaries`,
    defaultMessage: 'Summaries',
  },
  seeAll: {
    id: `${scope}.seeAll`,
    defaultMessage: 'See all',
  },
  results: {
    id: `${scope}.results`,
    defaultMessage: 'results',
  },
  resultsFor: {
    id: `${scope}.resultsFor`,
    defaultMessage: 'result for',
  },
  noResultsFor: {
    id: `${scope}.noResultsFor`,
    defaultMessage: 'No {value} found for',
  },
  noResultsFoundFor: {
    id: `${scope}.noResultsFoundFor`,
    defaultMessage: 'No result found for',
  },
  tryAgain: {
    id: `${scope}.tryAgain`,
    defaultMessage: 'Please try again!',
  },
  youMayLike: {
    id: `${scope}.youMayLike`,
    defaultMessage: 'You may like',
  },
  otherSearches: {
    id: `${scope}.otherSearches`,
    defaultMessage: 'Other searches',
  },
  trendingAudioBooks: {
    id: `${scope}.trendingAudioBooks`,
    defaultMessage: 'Trending audiobooks',
  },
  freeAudioBooks: {
    id: `${scope}.freeAudioBooks`,
    defaultMessage: 'Free audiobooks',
  },
  includedAudioBooks: {
    id: `${scope}.includedAudioBooks`,
    defaultMessage: 'Included audiobooks for Members',
  },
  newlyPublishedAudioBooks: {
    id: `${scope}.newlyPublishedAudioBooks`,
    defaultMessage: 'Newly published audiobooks',
  },
  trendingEbooks: {
    id: `${scope}.trendingEbooks`,
    defaultMessage: 'Trending ebooks',
  },
  freeEbooks: {
    id: `${scope}.freeEbooks`,
    defaultMessage: 'Free ebooks',
  },
  newlyPublishedEbooks: {
    id: `${scope}.newlyPublishedEbooks`,
    defaultMessage: 'Newly published ebooks',
  },
  trendingBookSummaries: {
    id: `${scope}.trendingBookSummaries`,
    defaultMessage: 'Trending book summaries',
  },
  newlyPublishedBookSummaries: {
    id: `${scope}.newlyPublishedBookSummaries`,
    defaultMessage: 'Newly published book summaries',
  },
  [MediaType.BOOK]: {
    id: `${scope}.audioBooks`,
    defaultMessage: 'Audiobook',
  },
  [MediaType.BOOK_SUMMARY]: {
    id: `${scope}.summary`,
    defaultMessage: 'Summary',
  },
  [`${MediaType.BOOK_SUMMARY}_today_free`]: {
    id: `${scope}.summaryTodayFree`,
    defaultMessage: 'Today free book summary',
  },
  inviteFriendFirstHeadline: {
    id: `${scope}.inviteFriendFirstHeadline`,
    defaultMessage: 'Give & Get',
  },
  inviteFriendSecondHeadline: {
    id: `${scope}.inviteFriendSecondHeadline`,
    defaultMessage: 'Free audiobook',
  },
  podcastChannel: {
    id: `${scope}.podcastChannel`,
    defaultMessage: 'Podcast channel',
  },
  podcastEpisode: {
    id: `${scope}.podcastEpisode`,
    defaultMessage: 'Podcast episode',
  },
  author: {
    id: `${scope}.author`,
    defaultMessage: 'Author',
  },
  podCourse: {
    id: `${scope}.podCourse`,
    defaultMessage: 'PodCourse',
  },
  searchPageTitle: {
    id: `${scope}.searchPageTitle`,
    defaultMessage: 'searchPageTitle',
  },
  kids: {
    id: `${scope}.kids`,
    defaultMessage: 'kids',
  },
  podcast: {
    id: `${scope}.podcast`,
    defaultMessage: 'podcast',
  },
  newReleaseTitle: {
    id: `${scope}.newReleaseTitle`,
    defaultMessage: 'Newly released audiobook',
  },
  mostSearchedTitle: {
    id: `${scope}.mostSearchedTitle`,
    defaultMessage: 'Most searched',
  },
  summary: {
    id: `${scope}.summaryShort`,
    defaultMessage: 'Summary short',
  },
  musicCategory: {
    id: `${scope}.musicCategory`,
    defaultMessage: 'Music short',
  },
  reviewer: {
    id: `${scope}.reviewer`,
    defaultMessage: 'Reviewer',
  },
  topRanking: {
    id: `${scope}.topRanking`,
    defaultMessage: 'Top Ranking',
  },
  onFonos: {
    id: `${scope}.onFonos`,
    defaultMessage: 'Trên Fonos',
  },
  purchased: {
    id: `${scope}.purchased`,
    defaultMessage: 'Đã mua',
  },
  searchInLibrary: {
    id: `${scope}.searchInLibrary`,
    defaultMessage: 'Search in library',
  },
  searchInLibraryDesc: {
    id: `${scope}.searchInLibraryDesc`,
    defaultMessage: 'Find all content in your library',
  },
})
