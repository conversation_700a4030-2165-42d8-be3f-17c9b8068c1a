import { useEffect, useState } from 'react'

import { getMigrationManager } from 'Services/Database/Migrations/MigrationManager'
import { ALL_MIGRATIONS } from 'Services/Database/Migrations/tasks'
import { MigrationState } from 'Services/Database/Migrations/types'
import { getCurrentUser } from 'Features/Auth/Stores'

export interface AppMigrationStatus {
  state: MigrationState
  error: Error | null
  requiresRestart: boolean
  currentTaskDescription: string | null
  currentTaskKey: string | null
  completedTasksCount: number
  totalTasksCount: number
  progressPercent: number
}

const useAppMigrationManager = (): AppMigrationStatus => {
  const [status, setStatus] = useState<AppMigrationStatus>({
    state: MigrationState.IDLE,
    error: null,
    requiresRestart: false,
    currentTaskDescription: null,
    currentTaskKey: null,
    completedTasksCount: 0,
    totalTasksCount: ALL_MIGRATIONS.length,
    progressPercent: 0,
  })

  useEffect(() => {
    const migrationManager = getMigrationManager()
    const user = getCurrentUser()

    // Combine global and user progress observables
    const subscriptions: Array<{ unsubscribe: () => void }> = []

    // Subscribe to global migrations progress
    const globalSub = migrationManager
      .getGlobalProgress$()
      .subscribe(globalProgress => {
        // If we have a user, also monitor user migrations
        if (user?.id) {
          const userSub = migrationManager
            .getUserProgress$(user.id)
            .subscribe(userProgress => {
              // Combine both progresses
              const totalTasks =
                globalProgress.totalCount + userProgress.totalCount
              const completedTasks =
                globalProgress.completedCount + userProgress.completedCount

              // Normalize states - if totalCount is 0, treat IDLE as COMPLETED
              const normalizedGlobalState =
                globalProgress.totalCount === 0 &&
                  globalProgress.state === MigrationState.IDLE
                  ? MigrationState.COMPLETED
                  : globalProgress.state
              const normalizedUserState =
                userProgress.totalCount === 0 &&
                  userProgress.state === MigrationState.IDLE
                  ? MigrationState.COMPLETED
                  : userProgress.state

              // Determine overall state - prioritize ERROR and MIGRATING states
              let overallState = MigrationState.COMPLETED
              if (
                normalizedGlobalState === MigrationState.ERROR ||
                normalizedUserState === MigrationState.ERROR
              ) {
                overallState = MigrationState.ERROR
              } else if (
                normalizedGlobalState === MigrationState.MIGRATING ||
                normalizedUserState === MigrationState.MIGRATING
              ) {
                overallState = MigrationState.MIGRATING
              } else if (
                normalizedGlobalState === MigrationState.PENDING_CHECK ||
                normalizedUserState === MigrationState.PENDING_CHECK
              ) {
                overallState = MigrationState.PENDING_CHECK
              } else if (
                normalizedGlobalState === MigrationState.IDLE ||
                normalizedUserState === MigrationState.IDLE
              ) {
                overallState = MigrationState.IDLE
              } else {
                // Both are COMPLETED
                overallState = MigrationState.COMPLETED
              }

              // Get current migration description
              const currentMigration =
                globalProgress.currentMigration || userProgress.currentMigration
              const currentMigrationData = ALL_MIGRATIONS.find(
                m => m.key === currentMigration,
              )

              setStatus({
                state: overallState,
                error: null, // We'd need to enhance MigrationManager to expose errors
                requiresRestart: false, // Will be set via separate check
                currentTaskKey: currentMigration,
                currentTaskDescription:
                  currentMigrationData?.description || null,
                completedTasksCount: completedTasks,
                totalTasksCount: totalTasks,
                progressPercent:
                  totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 100,
              })
            })

          subscriptions.push({ unsubscribe: () => userSub.unsubscribe() })
        } else {
          // No user, only monitor global migrations
          const currentMigrationData = ALL_MIGRATIONS.find(
            m => m.key === globalProgress.currentMigration,
          )

          setStatus({
            state:
              globalProgress.totalCount === 0
                ? MigrationState.COMPLETED
                : globalProgress.state,
            error: null,
            requiresRestart: false,
            currentTaskKey: globalProgress.currentMigration,
            currentTaskDescription: currentMigrationData?.description || null,
            completedTasksCount: globalProgress.completedCount,
            totalTasksCount: globalProgress.totalCount,
            progressPercent:
              globalProgress.totalCount > 0
                ? (globalProgress.completedCount / globalProgress.totalCount) *
                100
                : 100,
          })
        }
      })

    subscriptions.push({ unsubscribe: () => globalSub.unsubscribe() })

    return () => {
      subscriptions.forEach(sub => sub.unsubscribe())
    }
  }, [])

  return status
}

export default useAppMigrationManager
