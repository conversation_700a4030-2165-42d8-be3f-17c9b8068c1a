import type {
  PlaybackActiveTrackChangedEvent,
  PlaybackErrorEvent,
  PlaybackProgressUpdatedEvent,
  PlaybackQueueEndedEvent,
  PlaybackState,
  RemoteJumpBackwardEvent,
  RemoteJumpForwardEvent,
  RemoteSeekEvent,
} from '@vmsilva/react-native-track-player'
import { ceil, isUndefined } from 'lodash'
import { AppState, NativeModules, Platform } from 'react-native'

import AdsController from 'Services/Ads/AdsController'
import AdPlayerService from 'Services/Ads/Services/AdPlayerService'
import PlayerPlaybackEventTracker from 'Services/PlayMedia/PlayerPlaybackEventTracker'
import {
  getCurrentPlayMedia,
  isCurrentPlayMediaFree,
} from 'Services/PlayMedia/Store/PlayMediaStore'
import ProgressController from 'Services/Progress/Controller/ProgressController'
import { SleepTimerStore } from 'Features/PlayerModal/Store/SleepTimerStore'
import PlayerEventListener, { PlayerEvent } from 'Player/PlayerEventListener'
import PlayerService from 'Player/PlayerService'
import { Event, State } from 'Player/Services/TrackPlayerService'
import { PlayerStore, updatePlayerState } from 'Player/Store/PlayerStore'

import RemotePlayerService from './RemotePlayerService'
import { TrackPlayer } from './TrackPlayerService'
import { updateCurrentTrackIndex } from './utils'

interface RNCarPlayProps {
  navigateToNowPlaying: () => void
}

const RNCarPlay: RNCarPlayProps = NativeModules.RNCarPlay

let wasPausedByDuck = false

export async function PlaybackService() {
  let lastPosition = 0

  TrackPlayer.addEventListener(
    Event.PlaybackQueueEnded,
    (e: PlaybackQueueEndedEvent) => {
      // Should Update Progress Incase App Paused or Finished From Background
      if (PlayerStore.get().isReady) {
        PlayerStore.get().setProgress({
          position: lastPosition,
        })
      }
      PlayerPlaybackEventTracker.onPlaybackQueueEndedEvent()
      PlayerEventListener.debounceTriggerEvent(PlayerEvent.QueueEnded)
      if (e?.position) {
        const position = ceil(e?.position, 2)

        // this is first track
        if (e?.track === 0)
          PlayerPlaybackEventTracker.onRequestPurchaseBook(position)
        // update right away position on end  without wait 5s
        ProgressController.trackProgressUpdated(position)
      }
    },
  )

  TrackPlayer.addEventListener(
    Event.PlaybackActiveTrackChanged,
    async (event: PlaybackActiveTrackChangedEvent) => {
      if (PlayerStore.get().isReady) {
        const {
          pauseWhenEndChapter,
          setPauseWhenEndChapter,
          setShouldResetTimer,
        } = SleepTimerStore.get()
        if (!PlayerStore.get().autoPlayOnTrackChange || pauseWhenEndChapter) {
          PlayerService.togglePause()
          if (pauseWhenEndChapter) {
            setPauseWhenEndChapter(false)
            setShouldResetTimer(true)
          }
        } else {
          setTimeout(() => PlayerService.togglePlay(), 300)
        }
      }

      const isNewTrack = !isUndefined(event?.index)
      const currentPlayMedia = getCurrentPlayMedia()
      if (isNewTrack) {
        AdsController.interruptAdPlaying().then(() => {
          if (
            AdsController.isAllowMediaTypes(currentPlayMedia?.mediaType) &&
            isCurrentPlayMediaFree()
          ) {
            AdsController.handleAdPlaybackOnTrackChange()
          }
        })
      }

      updateCurrentTrackIndex(event.index)
      PlayerPlaybackEventTracker.onPlaybackTrackChangedEvent(event)
      PlayerEventListener.debounceTriggerEvent(
        PlayerEvent.PlaybackActiveTrackChanged,
      )
    },
  )

  TrackPlayer.addEventListener(
    Event.PlaybackState,
    async (event: PlaybackState) => {
      PlayerPlaybackEventTracker.onPlaybackStateEvent(event)
      updatePlayerState(event?.state)

      switch (event.state) {
        case State.Loading: {
          if (Platform.OS === 'ios') RNCarPlay.navigateToNowPlaying()
          break
        }
        case State.Playing: {
          if (AdPlayerService.isPlaying()) {
            PlayerService.togglePause()
          } else {
            PlayerEventListener.debounceTriggerEvent(PlayerEvent.Playing)
          }
          break
        }

        case State.Paused:
          PlayerEventListener.debounceTriggerEvent(PlayerEvent.Paused)
          PlayerStore.get().setProgress({
            position: lastPosition,
          })
          break
        case State.Stopped:
          PlayerStore.get().setProgress({
            position: lastPosition,
          })
          break
      }
    },
  )

  TrackPlayer.addEventListener(
    Event.PlaybackError,
    (event: PlaybackErrorEvent) => {
      PlayerPlaybackEventTracker.onPlaybackErrorEvent(event)
    },
  )

  TrackPlayer.addEventListener(
    Event.PlaybackProgressUpdated,
    async (event: PlaybackProgressUpdatedEvent) => {
      lastPosition = ceil(event.position, 2)
      const position = ceil(event.position, 2)
      if (AppState.currentState === 'active' && PlayerStore.get().isReady) {
        PlayerStore.get().setProgress({
          position: position,
          duration: event.duration,
          buffered: event.buffered,
        })
      }

      // this is first chapter
      if (event.track === 0) {
        PlayerPlaybackEventTracker.onFinishFirstChapterTracking({ position })
      }
      // Sometime when first time fire event, position is 0. It make Store update progress to 0. If user close before 5s. It rollback progress player to 0.
      // Position rarely return absolute 0. So we need to check if position is 0, we should not update progress.
      if (position) {
        let track: any
        try {
          track = await TrackPlayer.getActiveTrack()
        } catch (e) { }
        ProgressController.throttleTrackProgressUpdated(
          position,
          track,
          event.track,
        )
      }
    },
  )

  TrackPlayer.addEventListener(Event.RemotePause, () => {
    TrackPlayer.pause()
    // Should Update Progress Incase App Paused or Finished From Background
    if (PlayerStore.get().isReady) {
      PlayerStore.get().setProgress({
        position: lastPosition,
      })
    }
  })

  TrackPlayer.addEventListener(Event.RemotePlay, () => {
    TrackPlayer.play()
  })

  TrackPlayer.addEventListener(Event.RemoteNext, async () => {
    PlayerService.skipToNextTrack()
  })

  TrackPlayer.addEventListener(Event.RemotePrevious, async () => {
    PlayerService.skipToPreviousTrack()
  })

  TrackPlayer.addEventListener(Event.RemoteSeek, (event: RemoteSeekEvent) => {
    RemotePlayerService.onSeek(event?.position)
  })

  TrackPlayer.addEventListener(
    Event.RemoteJumpBackward,
    async (event: RemoteJumpBackwardEvent) => {
      PlayerService.jumpBackward(event?.interval)
    },
  )

  TrackPlayer.addEventListener(
    Event.RemoteJumpForward,
    (event: RemoteJumpForwardEvent) => {
      PlayerService.jumpForward(event?.interval)
    },
  )

  TrackPlayer.addEventListener(
    Event.RemoteDuck,
    async ({ permanent, paused }) => {
      if (permanent) {
        TrackPlayer.pause()
        return
      }
      if (paused) {
        if (Platform.OS === 'ios') {
          wasPausedByDuck = true
        } else {
          const playerState = await TrackPlayer.getState()
          wasPausedByDuck = playerState !== State.Paused
          TrackPlayer.pause()
        }
      } else {
        if (wasPausedByDuck) {
          TrackPlayer.play()
          wasPausedByDuck = false
        }
      }
    },
  )
}
