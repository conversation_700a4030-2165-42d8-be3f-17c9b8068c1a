import RNTrackPlayer, {
  AppKilledPlaybackBehavior,
  Capability,
  Event,
  IOSCategoryOptions,
  PitchAlgorithm,
  PlayerOptions,
  RepeatMode,
  State,
  Track,
  TrackMetadataBase,
  TrackType,
  UpdateOptions,
  useTrackPlayerEvents,
} from '@vmsilva/react-native-track-player'
import { Platform } from 'react-native'

import { UserService } from 'Repo/applications/user'
import { MediaType } from 'Repo/constants/media'
import { MissionService } from 'Services/Challenge'
import { PlayerModalStore } from 'Features/PlayerModal/Store/PlayerModalStore'
import { isSameDate } from 'Utils/datetime'

const FIVE_GIGABYTES = 5 * 1024 * 1024 * 1024

class TrackPlayerService {
  _trackPlayer: typeof RNTrackPlayer
  private _isSetup: boolean
  private _rate: number = 1
  private _lastPause: Date

  constructor() {
    this._trackPlayer = RNTrackPlayer
    if (Platform.OS === 'ios') {
      this.setup()
    }
  }

  public get isSetup() {
    return this._isSetup
  }

  async setup() {
    try {
      // try to reset first to if native player already
      await this._trackPlayer.reset()
    } catch (_) {
    } finally {
      try {
        await this._trackPlayer.setupPlayer({
          playBuffer: 2,
          maxBuffer: 180,
          minBuffer: 10,
          backBuffer: 30,
          waitForBuffer: true, //Don't remove this field. The library is not correct, it default is false. Not default is true like document.
          iosCategoryOptions: [
            IOSCategoryOptions.AllowAirPlay,
            IOSCategoryOptions.AllowBluetooth,
            IOSCategoryOptions.AllowBluetoothA2DP,
            IOSCategoryOptions.DuckOthers,
          ],
          maxCacheSize: FIVE_GIGABYTES,
        })
        this._isSetup = true
      } catch (error) {
        this._isSetup = false
      }
    }
  }

  registerPlaybackService(cb: any) {
    this._trackPlayer.registerPlaybackService(cb)
    // Delay to register playback service due to onStartCommand triggered by HeadlessJsTaskService
    // https://github.com/doublesymmetry/react-native-track-player/issues/2244
    // setTimeout(() => {
    //   console.log('registerPlaybackService')
    //   return
    // }, 3000)
  }

  skip(trackIndex: number, initialPosition?: number | undefined) {
    if (Number(trackIndex) < 0) return Promise.resolve()
    return this._trackPlayer.skip(trackIndex, initialPosition)
  }

  seekTo(position: number) {
    return this._trackPlayer.seekTo(position)
  }

  setVolume(val: number) {
    return this._trackPlayer.setVolume(val)
  }

  getVolume() {
    return this._trackPlayer.getVolume()
  }

  play() {
    if (this._lastPause) {
      // Note: Should try trigger mission ironman again when user keep play on tomorrow
      const isSameDay = isSameDate(new Date(), this._lastPause, 'd')
      if (!isSameDay) {
        const { mediaInfo } = PlayerModalStore.get()
        if (mediaInfo && mediaInfo.mediaType === MediaType.BOOK) {
          MissionService.trigger('start_play_content', {
            mediaType: mediaInfo.mediaType,
            isSample: false,
          })
          UserService.trackUserStreak()
        }
      }
    }
    return this._trackPlayer.play()
  }

  pause() {
    this._lastPause = new Date()
    return this._trackPlayer.pause()
  }

  async reset() {
    return this._trackPlayer.reset()
  }

  getRepeatMode() {
    return this._trackPlayer.getRepeatMode()
  }

  setPlayWhenReady(playWhenReady: boolean) {
    return this._trackPlayer.setPlayWhenReady(playWhenReady)
  }

  setRepeatMode(mode: RepeatMode) {
    return this._trackPlayer.setRepeatMode(mode)
  }

  add(tracks: Track | Track[], insertBeforeIndex?: number | undefined) {
    return this._trackPlayer.add(tracks as any, insertBeforeIndex)
  }

  addEventListener(event: Event, listener: (_: any) => Promise<void> | void) {
    return this._trackPlayer.addEventListener(event, listener)
  }

  getQueue() {
    return this._trackPlayer.getQueue()
  }

  remove(indexes: number | number[]) {
    return this._trackPlayer.remove(indexes as any)
  }

  skipToNext() {
    return this._trackPlayer.skipToNext()
  }

  skipToPrevious() {
    return this._trackPlayer.skipToPrevious()
  }

  getProgress() {
    return this._trackPlayer.getProgress()
  }

  async getState() {
    const playbackState = await this._trackPlayer.getPlaybackState()
    return playbackState?.state
  }

  getPlaybackRate() {
    return this._trackPlayer.getRate()
  }

  setupPlayer(options?: PlayerOptions | undefined) {
    return this._trackPlayer.setupPlayer(options)
  }

  updateOptions(options?: UpdateOptions | undefined) {
    return this._trackPlayer.updateOptions({
      android: {
        appKilledPlaybackBehavior:
          AppKilledPlaybackBehavior.StopPlaybackAndRemoveNotification,
        alwaysPauseOnInterruption: true,
      },
      forwardJumpInterval: 30,
      backwardJumpInterval: 15,
      progressUpdateEventInterval: 1,
      ...options,
    })
  }

  setRate(rate: number) {
    this._rate = rate
    if (this._isSetup) return this._trackPlayer.setRate(rate)
  }

  seekBy(interval: number) {
    return this._trackPlayer.seekBy(interval)
  }

  load(track: Track) {
    return this._trackPlayer.load(track)
  }

  retry() {
    return this._trackPlayer.retry()
  }

  getActiveTrack() {
    return this._trackPlayer.getActiveTrack()
  }
}

const TrackPlayer = new TrackPlayerService()

export {
  TrackPlayer,
  Event,
  UpdateOptions,
  PlayerOptions,
  RepeatMode,
  Track,
  AppKilledPlaybackBehavior,
  Capability,
  IOSCategoryOptions,
  TrackType,
  State,
  useTrackPlayerEvents,
  TrackMetadataBase,
  PitchAlgorithm,
  TrackPlayerService,
}
