import {
  type PlaybackActiveTrackChangedEvent,
  type PlaybackErrorEvent,
  type PlaybackState,
  State,
} from '@vmsilva/react-native-track-player'
import { debounce, first, get } from 'lodash'

import { ToastService } from 'Components/Toast'
import { MediaType } from 'Repo/constants/media'
import type { UserBook } from 'Repo/entities/userBook'
import InAppReview from 'Services/InAppReview'
import { IN_APP_REVIEW_EVENT } from 'Services/InAppReview/constants'
import { ModalService } from 'Services/Modal'
import NavigationService from 'Services/NavigationService'
import PlayTracksStore from 'Services/Playlist/Store/PlayTracksStore'
import {
  getCurrentPlayMedia,
  PlayMediaStore,
} from 'Services/PlayMedia/Store/PlayMediaStore'
import { hasUserAuthenticated } from 'Features/Auth/Stores'
import { goToLogin } from 'Features/Auth/utils'
import BugReportService from 'Features/BugReport/BugReportService'
import { PlayerModalVisibility } from 'Features/PlayerModal/constants'
import PlayerModalService from 'Features/PlayerModal/PlayModalService'
import PurchaseService from 'Features/Purchase/Services/PurchaseService'
import MediaReviewService from 'Features/Reviews/Services/MediaReviewService'
import { BOOK_DETAIL_SCREEN_NAME } from 'Screens/HomeStack/HomeTab/constants'
import { isFinishTrack } from 'Utils/track'
import { formatMessage } from 'Translation/constants'
import Analytics from 'Trackings/Analytics'
import SentryAudioSessionTracker from 'Trackings/SentryAudioSessionTracker'
import { PlayerStore } from 'Player/Store/PlayerStore'

import messages from '../../Player/messages'
import PlayerController from '../../Player/PlayerController'
import PlayerService from '../../Player/PlayerService'

class PlayerPlaybackEventTracker {
  private _traceId?: string
  constructor() { }

  _retry() {
    const { position, increaseRetryCount } = PlayerStore.get()
    const { tracks: currentTracks, currentIndex } = PlayTracksStore.get()

    const initTrackAtIndex = currentIndex
    const initProgressAt = position

    PlayerController.playerReset()?.then(() => {
      PlayerService.play({
        tracks: currentTracks,
        isAutoPlay: true,
        initTrackAtIndex,
        initProgressAt,
      })

      increaseRetryCount()
    })
  }

  async onPlaybackStateEvent(event: PlaybackState) {
    switch (event?.state) {
      case State.Loading: {
        // const { tracks: currentTracks, currentIndex } = PlayTracksStore.get()
        // this._traceId = await startTracingCdnPerformance(
        //   currentTracks[currentIndex || 0],
        // )
        break
      }
      case State.Playing: {
        // if (this._traceId) {
        //   await stopTracingCdnPerformance(this._traceId, 1)
        //   this._traceId = undefined
        // }

        // Activate the prepared audio span when playback starts
        SentryAudioSessionTracker.startOrContinueAudioSession() // Renamed method
        break
      }
      case State.Paused: {
        // Stop the active audio span for pause (non-final)
        SentryAudioSessionTracker.stopCurrentAudioSession({
          // Renamed method
          status: 'cancelled' as unknown as any, // Use 'cancelled' status for user pause
          isFinal: false, // Pause is not a final stop
        })

        break
      }
      case State.Stopped: {
        // Stop the active audio span for stopped state (final)
        SentryAudioSessionTracker.stopCurrentAudioSession({
          // Renamed method
          status: 'ok' as unknown as any, // Use 'ok' status for normal stop
          isFinal: true, // Stop is a final state
        })
        break
      }
      case State.Error: {
        // Stop the active audio span for error state (final)
        SentryAudioSessionTracker.stopCurrentAudioSession({
          // Renamed method
          status: 'unknown_error' as unknown as any, // Use 'unknown_error' status
          isFinal: true, // Error is a final state
        })
        break
      }
      case State.Ready: {
        // if (this._traceId) {
        //   await stopTracingCdnPerformance(this._traceId, 1)
        //   this._traceId = undefined
        // }

        break
      }
    }
  }

  async onPlaybackErrorEvent(event: PlaybackErrorEvent) {
    ToastService.error(event.message || (event as any).error)
    // if (this._traceId) {
    //   await stopTracingCdnPerformance(this._traceId, 0)
    //   this._traceId = undefined
    // }

    // Stop the active audio span with error details when a playback error event occurs
    SentryAudioSessionTracker.stopCurrentAudioSession({
      status: 'unknown_error' as any, // Use 'unknown_error'
      message: `Playback error event: ${event.message || (event as any).error}`, // Keep the message for error context
      isFinal: true, // Error is a final state
    })

    const { retryCount } = PlayerStore.get()
    if (retryCount >= 3) {
      ModalService.notify({
        layout: 'modal',
        title: formatMessage(messages.playbackError),
        description: formatMessage(messages.playbackErrorDesc),
        confirmLabel: formatMessage(messages.contactNow),
        cancelLabel: formatMessage(messages.retry),
        onConfirm: () => {
          BugReportService.openTicketCreation({
            optionType: 'report_bug',
            issueType: 'other',
            description: event.message,
          })
        },
        onCancel: () => this._retry(),
      })
    }
  }

  // trigger when finish sample file
  public onRequestPurchaseBook(position: number) {
    const { tracks } = PlayTracksStore.get()
    const firstTrack = first(tracks)

    if (
      !firstTrack ||
      !firstTrack?.entityId ||
      ![MediaType.BOOK, MediaType.ENG_BOOK].includes(firstTrack?.entity)
    )
      return
    const firstTrackDuration = get(firstTrack, `duration`)

    if (!isFinishTrack(position, firstTrackDuration)) return
    const playerModalVisibility = PlayerModalService.getVisibility()

    let shouldSuggestPurchase = false
    if (playerModalVisibility === PlayerModalVisibility.SHOW) {
      shouldSuggestPurchase = true
    } else {
      const { name, params } = NavigationService.getCurrentRoute()
      if (
        name === BOOK_DETAIL_SCREEN_NAME &&
        params?.id === firstTrack?.entityId
      ) {
        shouldSuggestPurchase = true
      }
    }

    const currentMedia = getCurrentPlayMedia()
    if (shouldSuggestPurchase && currentMedia) {
      PlayerModalService.open()
      PurchaseService.purchaseSuggestion({
        ...currentMedia.media,
        mediaType: currentMedia?.mediaType,
      } as any)
    }
    this.onFinishFirstChapterTracking()
  }

  public onFinishFirstChapterTracking(opts?: { position: number }) {
    const { tracks } = PlayTracksStore.get()
    const { getTrackFinishStatus, setTrackFinishStatus } = PlayMediaStore.get()
    const firstTrack = first(tracks)

    if (
      !firstTrack ||
      !firstTrack?.entityId ||
      ![MediaType.BOOK, MediaType.ENG_BOOK].includes(firstTrack?.entity)
    )
      return

    if (opts) {
      const firstTrackDuration = get(firstTrack, `duration`)

      if (!isFinishTrack(Number(opts?.position) || 0, firstTrackDuration))
        return
    }

    const trackFinishStatus = getTrackFinishStatus({
      mediaType: firstTrack?.entity,
      id: firstTrack?.entityId,
    })

    if (!trackFinishStatus?.finishedFirstChapter) {
      debounce(() => {
        Analytics.finishFirstChapter({
          itemId: firstTrack?.entityId as any,
          mediaType: firstTrack?.entity as any,
        })
        InAppReview.showReview(IN_APP_REVIEW_EVENT.AFTER_CHAPTER_ONE)
      }, 1500)()
      setTrackFinishStatus({
        mediaType: firstTrack.entity,
        id: firstTrack.entityId,
        finishedFirstChapter: true,
      })
    }
  }

  async onPlaybackTrackChangedEvent(event: PlaybackActiveTrackChangedEvent) {
    // Stop the previous track's span when the track changes (final)
    SentryAudioSessionTracker.stopCurrentAudioSession({
      // Renamed method
      status: 'ok' as any, // Track change is considered a normal end ('ok') for the previous span
      isFinal: true,
    })

    if (!event) return
    if (event.lastIndex === 0 && event.lastPosition) {
      this.onFinishFirstChapterTracking()
    }
  }

  async onPlaybackQueueEndedEvent() {
    const currentPlayMedia = getCurrentPlayMedia()
    if (
      !currentPlayMedia ||
      !currentPlayMedia.media ||
      !currentPlayMedia?.mediaType
    )
      return
    const { getTrackFinishStatus, setTrackFinishStatus, reviewShown } =
      PlayMediaStore.get()
    const { mediaType, media, isSample } = currentPlayMedia
    const trackFinishStatus = getTrackFinishStatus({
      mediaType,
      id: media.id,
    })

    switch (mediaType) {
      case MediaType.BOOK: {
        if (!trackFinishStatus || !trackFinishStatus?.finished) {
          debounce(() => {
            Analytics.finishAudio({ itemId: media.id, mediaType })
          }, 1500)()
        }

        if ((media as UserBook)?.isFree && !hasUserAuthenticated())
          // sure that if not auth yet, finish track would be first chapter only
          goToLogin({ signInLayout: 'finish-free-listening' })

        break
      }
      case MediaType.BOOK_SUMMARY: {
        if (!trackFinishStatus || !trackFinishStatus?.finished) {
          debounce(() => {
            Analytics.finishAudio({ itemId: media.id, mediaType })
          }, 1500)()
        }

        break
      }

      case MediaType.MUSIC:
      case MediaType.SLEEP_STORY:
      case MediaType.KID_STORY:
      case MediaType.MEDITATION:
      case MediaType.GUIDE_MEDITATIONS:
        if (!trackFinishStatus || !trackFinishStatus?.finished) {
          debounce(() => {
            Analytics.finishZenAudio({
              itemId: media.id,
              mediaType,
              voice: media?.voice,
            })
          }, 1500)()
        }
        break

      default:
        break
    }

    if (!isSample && !reviewShown) {
      MediaReviewService.popupReview(mediaType, media.id)
      PlayMediaStore.set({ reviewShown: true })
    }

    setTrackFinishStatus({
      mediaType,
      id: media.id,
      finished: true,
    })
  }
}

export default new PlayerPlaybackEventTracker()
